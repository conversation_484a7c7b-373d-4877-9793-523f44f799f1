{"/api/admin/projects/[id]/builds/route": "/api/admin/projects/[id]/builds", "/api/admin/projects/route": "/api/admin/projects", "/api/admin/users/route": "/api/admin/users", "/api/debug/auth/route": "/api/debug/auth", "/api/health/route": "/api/health", "/api/projects/[id]/builds/[buildId]/route": "/api/projects/[id]/builds/[buildId]", "/api/projects/[id]/builds/revert/route": "/api/projects/[id]/builds/revert", "/api/projects/[id]/builds/route": "/api/projects/[id]/builds", "/api/projects/[id]/route": "/api/projects/[id]", "/api/streampixel/upload/route": "/api/streampixel/upload", "/api/streampixel/activate/route": "/api/streampixel/activate", "/api/upload/backblaze-direct/route": "/api/upload/backblaze-direct", "/api/upload/uppy/abort-multipart/route": "/api/upload/uppy/abort-multipart", "/api/upload/uppy/complete-multipart/route": "/api/upload/uppy/complete-multipart", "/api/upload/uppy/complete/route": "/api/upload/uppy/complete", "/api/upload/uppy/list-parts/route": "/api/upload/uppy/list-parts", "/api/upload/uppy/multipart-params/route": "/api/upload/uppy/multipart-params", "/api/upload/uppy/sign-part/route": "/api/upload/uppy/sign-part", "/api/upload/uppy/single-params/route": "/api/upload/uppy/single-params", "/api/webhooks/streampixel/route": "/api/webhooks/streampixel", "/favicon.ico/route": "/favicon.ico", "/_not-found/page": "/_not-found", "/admin/page": "/admin", "/admin/users/page": "/admin/users", "/admin/projects/[id]/page": "/admin/projects/[id]", "/dashboard/page": "/dashboard", "/login/page": "/login", "/embed/[projectId]/page": "/embed/[projectId]", "/page": "/", "/projects/[id]/page": "/projects/[id]", "/test-stream/page": "/test-stream"}