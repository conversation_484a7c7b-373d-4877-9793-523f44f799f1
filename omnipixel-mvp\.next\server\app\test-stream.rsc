1:"$Sreact.fragment"
2:I[1089,["4134","static/chunks/4134-b800debdf1f787db.js","3865","static/chunks/3865-75d1da4177166428.js","7177","static/chunks/app/layout-9b7576475d23288f.js"],"AuthProvider"]
3:I[7555,[],""]
4:I[1295,[],""]
5:I[894,[],"ClientPageRoot"]
6:I[5261,["4306","static/chunks/210f6f31-283c356c4fc2b147.js","4134","static/chunks/4134-b800debdf1f787db.js","5389","static/chunks/5389-1df52f259077754e.js","9771","static/chunks/9771-dd53057942eb6cf7.js","6227","static/chunks/6227-4fd87ffb9cf914e5.js","6202","static/chunks/6202-4b58cd926976d70b.js","5580","static/chunks/5580-2efb7466b69acd85.js","9071","static/chunks/9071-faf43fbaea735c2f.js","8167","static/chunks/8167-3db40209284c55df.js","3566","static/chunks/app/test-stream/page-3db675b41962a72a.js"],"default"]
9:I[9665,[],"OutletBoundary"]
b:I[4911,[],"AsyncMetadataOutlet"]
d:I[9665,[],"ViewportBoundary"]
f:I[9665,[],"MetadataBoundary"]
10:"$Sreact.suspense"
12:I[8393,[],""]
:HL["/_next/static/css/fc4a08724541d608.css","style"]
0:{"P":null,"b":"jfeEtSiK_RbsNRAfZECR1","p":"","c":["","test-stream"],"i":false,"f":[[["",{"children":["test-stream",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/fc4a08724541d608.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]]}],{"children":["test-stream",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L5",null,{"Component":"$6","searchParams":{},"params":{},"promises":["$@7","$@8"]}],null,["$","$L9",null,{"children":["$La",["$","$Lb",null,{"promise":"$@c"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,[["$","$Ld",null,{"children":"$Le"}],null],["$","$Lf",null,{"children":["$","div",null,{"hidden":true,"children":["$","$10",null,{"fallback":null,"children":"$L11"}]}]}]]}],false]],"m":"$undefined","G":["$12",[]],"s":false,"S":true}
7:{}
8:"$0:f:0:1:2:children:2:children:1:props:children:0:props:params"
e:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
a:null
13:I[8175,[],"IconMark"]
c:{"metadata":[["$","title","0",{"children":"Omnipixel"}],["$","meta","1",{"name":"description","content":"Omnipixel - Serverless project dashboard and streaming platform"}],["$","link","2",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L13","3",{}]],"error":null,"digest":"$undefined"}
11:"$c:metadata"
