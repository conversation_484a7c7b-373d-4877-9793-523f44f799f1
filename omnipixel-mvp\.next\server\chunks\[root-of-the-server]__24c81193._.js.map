{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\r\nimport { cookies } from 'next/headers'\r\n\r\nexport async function createClient(url?:string, key?:string) {\r\n  const cookieStore = await cookies()\r\n  url = url ?? process.env.NEXT_PUBLIC_SUPABASE_URL!\r\n  key = key ?? process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY!\r\n\r\n  return createServerClient(\r\n    url,\r\n    key,\r\n    {\r\n      cookies: {\r\n        getAll() {\r\n          return cookieStore.getAll()\r\n        },\r\n        setAll(cookiesToSet) {\r\n          try {\r\n            cookiesToSet.forEach(({ name, value, options }) =>\r\n              cookieStore.set(name, value, options)\r\n            )\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  )\r\n}"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe,aAAa,GAAW,EAAE,GAAW;IACzD,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,MAAM;IACN,MAAM;IAEN,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EACtB,KACA,KACA;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/app/api/projects/%5Bid%5D/builds/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createClient } from '@/utils/supabase/server'\n\n// GET - Fetch all builds for a project\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    // Await params\n    const { id: projectId } = await params\n\n    // Create Supabase client for server-side auth\n    const supabase = await createClient()\n\n    // Get the current user\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\n    \n    if (authError || !user) {\n      return NextResponse.json(\n        { error: 'Unauthorized' },\n        { status: 401 }\n      )\n    }\n\n    // Verify user owns this project or is admin\n    const { data: profile, error: profileError } = await supabase\n      .from('profiles')\n      .select('role')\n      .eq('id', user.id)\n      .single()\n\n    if (profileError) {\n      return NextResponse.json(\n        { error: 'Failed to verify user profile' },\n        { status: 500 }\n      )\n    }\n\n    const isAdmin = profile?.role === 'platform_admin'\n\n    // Build query based on user role\n    const buildsQuery = supabase\n      .from('builds')\n      .select('*')\n      .eq('project_id', projectId)\n      .order('created_at', { ascending: false })\n\n    // If not admin, verify project ownership\n    if (!isAdmin) {\n      // First verify the user owns this project\n      const { data: project, error: projectError } = await supabase\n        .from('projects')\n        .select('id')\n        .eq('id', projectId)\n        .eq('user_id', user.id)\n        .single()\n\n      if (projectError || !project) {\n        return NextResponse.json(\n          { error: 'Project not found or access denied' },\n          { status: 404 }\n        )\n      }\n    }\n\n    // Fetch builds\n    const { data: builds, error: buildsError } = await buildsQuery\n\n    if (buildsError) {\n      console.error('Error fetching builds:', buildsError)\n      return NextResponse.json(\n        { error: 'Failed to fetch builds' },\n        { status: 500 }\n      )\n    }\n\n    return NextResponse.json({\n      builds: builds || []\n    })\n\n  } catch (error) {\n    console.error('Error in builds API:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,eAAe;QACf,MAAM,EAAE,IAAI,SAAS,EAAE,GAAG,MAAM;QAEhC,8CAA8C;QAC9C,MAAM,WAAW,MAAM,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;QAElC,uBAAuB;QACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAe,GACxB;gBAAE,QAAQ;YAAI;QAElB;QAEA,4CAA4C;QAC5C,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,YACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgC,GACzC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,UAAU,SAAS,SAAS;QAElC,iCAAiC;QACjC,MAAM,cAAc,SACjB,IAAI,CAAC,UACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc,WACjB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,yCAAyC;QACzC,IAAI,CAAC,SAAS;YACZ,0CAA0C;YAC1C,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,WACT,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,MAAM;YAET,IAAI,gBAAgB,CAAC,SAAS;gBAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAqC,GAC9C;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,eAAe;QACf,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM;QAEnD,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ,UAAU,EAAE;QACtB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}