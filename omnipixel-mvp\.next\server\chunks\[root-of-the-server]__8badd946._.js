module.exports = {

"[project]/.next-internal/server/app/api/projects/[id]/builds/[buildId]/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@aws-sdk/client-s3 [external] (@aws-sdk/client-s3, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@aws-sdk/client-s3", () => require("@aws-sdk/client-s3"));

module.exports = mod;
}}),
"[externals]/@aws-sdk/s3-request-presigner [external] (@aws-sdk/s3-request-presigner, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@aws-sdk/s3-request-presigner", () => require("@aws-sdk/s3-request-presigner"));

module.exports = mod;
}}),
"[project]/lib/backblaze-adapter.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "createBackblazeS3Client": ()=>createBackblazeS3Client,
    "default": ()=>__TURBOPACK__default__export__,
    "getBackblazeBucketName": ()=>getBackblazeBucketName,
    "getBackblazePublicUrl": ()=>getBackblazePublicUrl,
    "isBackblazeConfigured": ()=>isBackblazeConfigured
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@aws-sdk/client-s3 [external] (@aws-sdk/client-s3, cjs)");
;
function createBackblazeS3Client() {
    console.log('🔧 Creating Backblaze S3 Client...');
    // Get Backblaze credentials from environment
    const BACKBLAZE_APP_KEY_ID = process.env.BACKBLAZE_APP_KEY_ID;
    const BACKBLAZE_APP_KEY = process.env.BACKBLAZE_APP_KEY;
    console.log('Credentials check:');
    console.log('- BACKBLAZE_APP_KEY_ID:', BACKBLAZE_APP_KEY_ID ? `${BACKBLAZE_APP_KEY_ID.substring(0, 15)}...` : 'MISSING');
    console.log('- BACKBLAZE_APP_KEY:', BACKBLAZE_APP_KEY ? `${BACKBLAZE_APP_KEY.substring(0, 15)}...` : 'MISSING');
    // Backblaze endpoint - use the correct region for your bucket
    // See: https://www.backblaze.com/docs/cloud-storage-s3-compatible-api-endpoint-regions
    const BACKBLAZE_ENDPOINT = 'https://s3.us-east-005.backblazeb2.com';
    console.log('Configuration:');
    console.log('- Endpoint:', BACKBLAZE_ENDPOINT);
    console.log('- Region: us-east-005');
    console.log('- Force Path Style: true');
    if (!BACKBLAZE_APP_KEY_ID || !BACKBLAZE_APP_KEY) {
        console.error('❌ Backblaze credentials missing!');
        throw new Error('Backblaze credentials not configured');
    }
    // Create S3 client with Backblaze configuration
    const client = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["S3Client"]({
        region: 'us-east-005',
        endpoint: BACKBLAZE_ENDPOINT,
        credentials: {
            accessKeyId: BACKBLAZE_APP_KEY_ID,
            secretAccessKey: BACKBLAZE_APP_KEY
        },
        forcePathStyle: true
    });
    console.log('✅ Backblaze S3 Client created successfully');
    return client;
}
function getBackblazeBucketName() {
    return process.env.BACKBLAZE_BUCKET_NAME || 'omnipixel';
}
function isBackblazeConfigured() {
    return !!(process.env.BACKBLAZE_APP_KEY_ID && process.env.BACKBLAZE_APP_KEY && process.env.BACKBLAZE_BUCKET_NAME);
}
function getBackblazePublicUrl(key) {
    const bucketName = getBackblazeBucketName();
    // For us-east-005 region, use the correct download URL format
    return `https://f005.backblazeb2.com/file/${bucketName}/${key}`;
}
// Export adapter functions
const backblazeAdapter = {
    createBackblazeS3Client,
    getBackblazeBucketName,
    isBackblazeConfigured,
    getBackblazePublicUrl
};
const __TURBOPACK__default__export__ = backblazeAdapter;
}),
"[project]/lib/aws.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "BUCKET_NAME": ()=>BUCKET_NAME,
    "abortMultipartUpload": ()=>abortMultipartUpload,
    "checkS3ObjectExists": ()=>checkS3ObjectExists,
    "completeMultipartUpload": ()=>completeMultipartUpload,
    "createMultipartUpload": ()=>createMultipartUpload,
    "default": ()=>__TURBOPACK__default__export__,
    "deleteFileFromS3": ()=>deleteFileFromS3,
    "extractS3KeyFromUrl": ()=>extractS3KeyFromUrl,
    "generatePresignedDownloadUrl": ()=>generatePresignedDownloadUrl,
    "generatePresignedUploadUrl": ()=>generatePresignedUploadUrl,
    "generateS3Key": ()=>generateS3Key,
    "getContentTypeFromFilename": ()=>getContentTypeFromFilename,
    "getPublicS3FileUrl": ()=>getPublicS3FileUrl,
    "getS3FileUrl": ()=>getS3FileUrl,
    "isAWSConfigured": ()=>isAWSConfigured,
    "makeS3ObjectPublic": ()=>makeS3ObjectPublic,
    "s3Client": ()=>s3Client,
    "uploadFileToS3": ()=>uploadFileToS3,
    "validateFileType": ()=>validateFileType
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@aws-sdk/client-s3 [external] (@aws-sdk/client-s3, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$s3$2d$request$2d$presigner__$5b$external$5d$__$2840$aws$2d$sdk$2f$s3$2d$request$2d$presigner$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@aws-sdk/s3-request-presigner [external] (@aws-sdk/s3-request-presigner, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/backblaze-adapter.ts [app-route] (ecmascript)");
;
;
;
// Use Backblaze B2 with S3-compatible API instead of AWS S3
console.log('🔧 Initializing Backblaze S3 Client...');
console.log('Environment check:');
console.log('- BACKBLAZE_APP_KEY_ID:', process.env.BACKBLAZE_APP_KEY_ID ? `${process.env.BACKBLAZE_APP_KEY_ID.substring(0, 10)}...` : 'MISSING');
console.log('- BACKBLAZE_APP_KEY:', process.env.BACKBLAZE_APP_KEY ? `${process.env.BACKBLAZE_APP_KEY.substring(0, 10)}...` : 'MISSING');
console.log('- BACKBLAZE_BUCKET_NAME:', process.env.BACKBLAZE_BUCKET_NAME || 'MISSING');
const s3Client = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createBackblazeS3Client"])();
console.log('✅ Backblaze S3 Client created successfully');
const BUCKET_NAME = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getBackblazeBucketName"])();
function generateS3Key(userId, projectId, filename) {
    const timestamp = Date.now();
    const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_');
    // No omnipixel prefix needed since bucket is already named omnipixel
    return `uploads/${userId}/${projectId}/${timestamp}_${sanitizedFilename}`;
}
async function uploadFileToS3(key, file, contentType = 'application/octet-stream', metadata) {
    console.log('🚀 Starting uploadFileToS3...');
    console.log('Parameters:');
    console.log('- key:', key);
    console.log('- file size:', file.length, 'bytes');
    console.log('- contentType:', contentType);
    console.log('- metadata:', metadata);
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBackblazeConfigured"])()) {
        console.error('❌ Backblaze not configured!');
        console.error('Environment variables:');
        console.error('- BACKBLAZE_APP_KEY_ID:', process.env.BACKBLAZE_APP_KEY_ID ? 'SET' : 'MISSING');
        console.error('- BACKBLAZE_APP_KEY:', process.env.BACKBLAZE_APP_KEY ? 'SET' : 'MISSING');
        console.error('- BACKBLAZE_BUCKET_NAME:', process.env.BACKBLAZE_BUCKET_NAME ? 'SET' : 'MISSING');
        throw new Error('Backblaze credentials not configured');
    }
    console.log('✅ Backblaze configuration check passed');
    // Prepare S3 metadata (source of truth)
    const s3Metadata = {};
    if (metadata) {
        if (metadata.projectId) s3Metadata['project-id'] = metadata.projectId;
        if (metadata.userId) s3Metadata['user-id'] = metadata.userId;
        if (metadata.filename) s3Metadata['filename'] = metadata.filename;
        if (metadata.version) s3Metadata['version'] = metadata.version;
        if (metadata.streamPixelProcess !== undefined) {
            s3Metadata['streampixel-process'] = metadata.streamPixelProcess ? 'true' : 'false';
        }
        s3Metadata['uploaded-at'] = new Date().toISOString();
    }
    console.log('📦 Creating PutObjectCommand with:');
    console.log('- Bucket:', BUCKET_NAME);
    console.log('- Key:', key);
    console.log('- ContentType:', contentType);
    console.log('- Metadata:', s3Metadata);
    const command = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["PutObjectCommand"]({
        Bucket: BUCKET_NAME,
        Key: key,
        Body: file,
        ContentType: contentType,
        Metadata: Object.keys(s3Metadata).length > 0 ? s3Metadata : undefined
    });
    console.log('📡 Sending command to Backblaze B2...');
    console.log('S3 Client config:', {
        region: s3Client.config.region,
        endpoint: s3Client.config.endpoint
    });
    try {
        const result = await s3Client.send(command);
        console.log('✅ Upload successful! Result:', result);
        console.log('File uploaded to Backblaze B2 with metadata:', s3Metadata);
        const publicUrl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getBackblazePublicUrl"])(key);
        console.log('🔗 Generated public URL:', publicUrl);
        return publicUrl;
    } catch (error) {
        console.error('❌ Upload failed with error:', error);
        // Type-safe error handling
        const errorDetails = {};
        if (error && typeof error === 'object') {
            const errorObj = error;
            errorDetails.name = errorObj.name;
            errorDetails.message = errorObj.message;
            errorDetails.code = errorObj.Code;
            errorDetails.statusCode = errorObj.$metadata?.httpStatusCode;
            errorDetails.requestId = errorObj.$metadata?.requestId;
            errorDetails.fault = errorObj.$fault;
            errorDetails.region = errorObj.$metadata?.region;
        }
        console.error('Error details:', errorDetails);
        // Additional debugging for AccessDenied errors
        if (errorDetails.code === 'AccessDenied') {
            console.error('🔍 AccessDenied debugging:');
            console.error('- Check if App Key has writeFiles permission');
            console.error('- Check if App Key is scoped to the correct bucket');
            console.error('- Verify bucket name matches exactly');
            console.error('- Current bucket name:', BUCKET_NAME);
            console.error('- App Key ID:', process.env.BACKBLAZE_APP_KEY_ID?.substring(0, 10) + '...');
        }
        throw error;
    }
}
async function generatePresignedUploadUrl(key, contentType, uploadId, partNumber, expiresIn = 3600 // 1 hour
) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBackblazeConfigured"])()) {
        throw new Error('Backblaze credentials not configured');
    }
    // For multipart upload parts
    if (uploadId && partNumber) {
        const command = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["UploadPartCommand"]({
            Bucket: BUCKET_NAME,
            Key: key,
            UploadId: uploadId,
            PartNumber: partNumber
        });
        return await (0, __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$s3$2d$request$2d$presigner__$5b$external$5d$__$2840$aws$2d$sdk$2f$s3$2d$request$2d$presigner$2c$__cjs$29$__["getSignedUrl"])(s3Client, command, {
            expiresIn
        });
    }
    // For single file upload
    const command = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["PutObjectCommand"]({
        Bucket: BUCKET_NAME,
        Key: key,
        ContentType: contentType
    });
    return await (0, __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$s3$2d$request$2d$presigner__$5b$external$5d$__$2840$aws$2d$sdk$2f$s3$2d$request$2d$presigner$2c$__cjs$29$__["getSignedUrl"])(s3Client, command, {
        expiresIn
    });
}
async function generatePresignedDownloadUrl(key, expiresIn = 3600 // 1 hour
) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBackblazeConfigured"])()) {
        throw new Error('Backblaze credentials not configured');
    }
    const command = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["GetObjectCommand"]({
        Bucket: BUCKET_NAME,
        Key: key
    });
    return await (0, __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$s3$2d$request$2d$presigner__$5b$external$5d$__$2840$aws$2d$sdk$2f$s3$2d$request$2d$presigner$2c$__cjs$29$__["getSignedUrl"])(s3Client, command, {
        expiresIn
    });
}
async function deleteFileFromS3(key) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBackblazeConfigured"])()) {
        throw new Error('Backblaze credentials not configured');
    }
    const command = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["DeleteObjectCommand"]({
        Bucket: BUCKET_NAME,
        Key: key
    });
    await s3Client.send(command);
}
async function createMultipartUpload(key, contentType = 'application/octet-stream') {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBackblazeConfigured"])()) {
        throw new Error('Backblaze credentials not configured');
    }
    const command = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["CreateMultipartUploadCommand"]({
        Bucket: BUCKET_NAME,
        Key: key,
        ContentType: contentType
    });
    const response = await s3Client.send(command);
    if (!response.UploadId) {
        throw new Error('Failed to create multipart upload');
    }
    return {
        uploadId: response.UploadId
    };
}
async function completeMultipartUpload(key, uploadId, parts) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBackblazeConfigured"])()) {
        throw new Error('Backblaze credentials not configured');
    }
    const command = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["CompleteMultipartUploadCommand"]({
        Bucket: BUCKET_NAME,
        Key: key,
        UploadId: uploadId,
        MultipartUpload: {
            Parts: parts.sort((a, b)=>a.PartNumber - b.PartNumber)
        }
    });
    const response = await s3Client.send(command);
    return response.Location || (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getBackblazePublicUrl"])(key);
}
async function abortMultipartUpload(key, uploadId) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBackblazeConfigured"])()) {
        throw new Error('Backblaze credentials not configured');
    }
    const command = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["AbortMultipartUploadCommand"]({
        Bucket: BUCKET_NAME,
        Key: key,
        UploadId: uploadId
    });
    await s3Client.send(command);
}
function isAWSConfigured() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBackblazeConfigured"])();
}
function getS3FileUrl(key) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getBackblazePublicUrl"])(key);
}
function getPublicS3FileUrl(key) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getBackblazePublicUrl"])(key);
}
async function makeS3ObjectPublic(key) {
    if (!isAWSConfigured()) {
        throw new Error('Backblaze is not configured');
    }
    try {
        // First check if the object exists and is not a delete marker
        try {
            await s3Client.send(new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["GetObjectCommand"]({
                Bucket: BUCKET_NAME,
                Key: key
            }));
        } catch (headError) {
            if (headError && typeof headError === 'object' && 'name' in headError && headError.name === 'NoSuchKey') {
                throw new Error(`File not found in Backblaze B2: ${key}`);
            }
            if (headError && typeof headError === 'object' && 'name' in headError && headError.name === 'MethodNotAllowed') {
                throw new Error(`File is a delete marker (deleted file): ${key}`);
            }
            throw headError;
        }
        // If object exists, set ACL to public-read
        await s3Client.send(new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["PutObjectAclCommand"]({
            Bucket: BUCKET_NAME,
            Key: key,
            ACL: 'public-read'
        }));
        console.log(`Made Backblaze B2 object public: ${key}`);
    } catch (error) {
        console.error('Error making Backblaze B2 object public:', error);
        // Provide more specific error messages
        if (error && typeof error === 'object' && 'name' in error && 'ResourceType' in error && error.name === 'MethodNotAllowed' && error.ResourceType === 'DeleteMarker') {
            throw new Error(`Cannot make file public: File was deleted and only a delete marker exists. Please re-upload the file.`);
        }
        throw error;
    }
}
async function checkS3ObjectExists(key) {
    if (!isAWSConfigured()) {
        throw new Error('Backblaze is not configured');
    }
    try {
        // List object versions to check for delete markers
        const versionsResponse = await s3Client.send(new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["ListObjectVersionsCommand"]({
            Bucket: BUCKET_NAME,
            Prefix: key,
            MaxKeys: 10
        }));
        const versions = versionsResponse.Versions || [];
        const deleteMarkers = versionsResponse.DeleteMarkers || [];
        // Check if there's a current version (not deleted)
        const currentVersion = versions.find((v)=>v.Key === key && v.IsLatest);
        const currentDeleteMarker = deleteMarkers.find((dm)=>dm.Key === key && dm.IsLatest);
        return {
            exists: !!currentVersion && !currentDeleteMarker,
            isDeleteMarker: !!currentDeleteMarker,
            versions: [
                ...versions,
                ...deleteMarkers
            ]
        };
    } catch (error) {
        console.error('Error checking S3 object:', error);
        return {
            exists: false,
            isDeleteMarker: false
        };
    }
}
function extractS3KeyFromUrl(url) {
    const match = url.match(/https:\/\/[^\/]+\.s3\.[^\/]+\.amazonaws\.com\/(.+)/);
    return match ? match[1] : null;
}
function validateFileType(filename, allowedTypes = []) {
    const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    const defaultAllowedTypes = [
        '.zip',
        '.exe',
        '.apk',
        '.ipa',
        '.dmg',
        '.pkg',
        '.deb',
        '.rpm'
    ];
    const typesToCheck = allowedTypes.length > 0 ? allowedTypes : defaultAllowedTypes;
    return typesToCheck.includes(extension);
}
function getContentTypeFromFilename(filename) {
    const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    const contentTypes = {
        '.zip': 'application/zip',
        '.rar': 'application/x-rar-compressed',
        '.7z': 'application/x-7z-compressed',
        '.tar.gz': 'application/gzip',
        '.exe': 'application/x-msdownload',
        '.app': 'application/octet-stream',
        '.dmg': 'application/x-apple-diskimage',
        '.deb': 'application/vnd.debian.binary-package',
        '.rpm': 'application/x-rpm',
        '.apk': 'application/vnd.android.package-archive',
        '.ipa': 'application/octet-stream',
        '.aab': 'application/octet-stream',
        '.unity3d': 'application/octet-stream',
        '.unitypackage': 'application/octet-stream'
    };
    return contentTypes[extension] || 'application/octet-stream';
}
const awsLib = {
    s3Client,
    BUCKET_NAME,
    generateS3Key,
    uploadFileToS3,
    generatePresignedUploadUrl,
    generatePresignedDownloadUrl,
    deleteFileFromS3,
    createMultipartUpload,
    completeMultipartUpload,
    abortMultipartUpload,
    isAWSConfigured,
    getS3FileUrl,
    getPublicS3FileUrl,
    makeS3ObjectPublic,
    checkS3ObjectExists,
    extractS3KeyFromUrl,
    validateFileType,
    getContentTypeFromFilename
};
const __TURBOPACK__default__export__ = awsLib;
}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/utils/supabase/server.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "createClient": ()=>createClient
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
;
;
async function createClient(url, key) {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    url = url ?? ("TURBOPACK compile-time value", "https://qrnstvofnizsgdlubtbt.supabase.co");
    key = key ?? ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFybnN0dm9mbml6c2dkbHVidGJ0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyNzE1MDMsImV4cCI6MjA2ODg0NzUwM30.D0KmXra-FWDKBlJUvlDOmDcIXdC1mquCUA1vQFfpFVU");
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createServerClient"])(url, key, {
        cookies: {
            getAll () {
                return cookieStore.getAll();
            },
            setAll (cookiesToSet) {
                try {
                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));
                } catch  {
                // The `setAll` method was called from a Server Component.
                // This can be ignored if you have middleware refreshing
                // user sessions.
                }
            }
        }
    });
}
}),
"[project]/app/api/projects/[id]/builds/[buildId]/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "DELETE": ()=>DELETE,
    "PATCH": ()=>PATCH
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$aws$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/aws.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-route] (ecmascript)");
;
;
;
async function DELETE(request, { params }) {
    try {
        // Create Supabase client for server-side auth
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])();
        // Get the current user
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        const { id: projectId, buildId } = await params;
        // Verify user owns this project or is admin
        const { data: profile, error: profileError } = await supabase.from('profiles').select('role').eq('id', user.id).single();
        if (profileError) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Failed to verify user profile'
            }, {
                status: 500
            });
        }
        const isAdmin = profile?.role === 'platform_admin';
        // If not admin, verify project ownership
        if (!isAdmin) {
            const { data: project, error: projectError } = await supabase.from('projects').select('id').eq('id', projectId).eq('user_id', user.id).single();
            if (projectError || !project) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Project not found or access denied'
                }, {
                    status: 404
                });
            }
        }
        // Get the build to delete
        const { data: build, error: buildError } = await supabase.from('builds').select('*').eq('id', buildId).eq('project_id', projectId).single();
        if (buildError || !build) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Build not found'
            }, {
                status: 404
            });
        }
        // Check if this is the only build - prevent deletion if so
        const { data: allBuilds, error: countError } = await supabase.from('builds').select('id').eq('project_id', projectId).neq('status', 'failed');
        if (countError) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Failed to check build count'
            }, {
                status: 500
            });
        }
        // Allow deletion even if it's the only build - user might want to start fresh
        // Delete from Backblaze B2 first
        try {
            if (build.s3_key) {
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$aws$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["deleteFileFromS3"])(build.s3_key);
            }
        } catch (b2Error) {
            console.error('Error deleting from Backblaze B2:', b2Error);
        // Continue with database deletion even if B2 deletion fails
        // The file might already be deleted or the key might be invalid
        }
        // Delete from database
        const { error: deleteError } = await supabase.from('builds').delete().eq('id', buildId);
        if (deleteError) {
            console.error('Error deleting build from database:', deleteError);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Failed to delete build'
            }, {
                status: 500
            });
        }
        // If we deleted the current build, check if there's another build to make current
        if (build.is_current && allBuilds.length > 1) {
            // Find the most recent remaining build
            const { data: remainingBuilds, error: remainingError } = await supabase.from('builds').select('*').eq('project_id', projectId).neq('status', 'failed').order('version', {
                ascending: false
            }).limit(1);
            if (!remainingError && remainingBuilds && remainingBuilds.length > 0) {
                // Set the most recent build as current
                await supabase.from('builds').update({
                    is_current: true,
                    status: 'active',
                    updated_at: new Date().toISOString()
                }).eq('id', remainingBuilds[0].id);
            }
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            message: 'Build deleted successfully',
            deletedBuild: {
                id: build.id,
                filename: build.filename,
                version: build.version
            }
        });
    } catch (error) {
        console.error('Error deleting build:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function PATCH(request, { params }) {
    try {
        // Await params
        const { id: projectId, buildId } = await params;
        // Create Supabase client for server-side auth
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])();
        // Get the current user
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) {
            console.error('Authentication error:', authError);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        console.log('Authenticated user:', user.id);
        // projectId and buildId already extracted from awaited params above
        // Verify user owns this project or is admin
        const { data: profile, error: profileError } = await supabase.from('profiles').select('role').eq('id', user.id).single();
        if (profileError) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Failed to verify user profile'
            }, {
                status: 500
            });
        }
        const isAdmin = profile?.role === 'platform_admin';
        // Get project details (needed for StreamPixel integration)
        let projectQuery;
        if (isAdmin) {
            projectQuery = supabase.from('projects').select('*').eq('id', projectId);
        } else {
            projectQuery = supabase.from('projects').select('*').eq('id', projectId).eq('user_id', user.id);
        }
        const { data: project, error: projectError } = await projectQuery.single();
        if (projectError || !project) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Project not found or access denied'
            }, {
                status: 404
            });
        }
        // Get the build to activate
        console.log('Looking for build:', buildId, 'in project:', projectId);
        const { data: build, error: buildError } = await supabase.from('builds').select('*').eq('id', buildId).eq('project_id', projectId).single();
        if (buildError) {
            console.error('Error finding build:', buildError);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: `Build not found: ${buildError.message}`
            }, {
                status: 404
            });
        }
        if (!build) {
            console.error('Build not found - no data returned');
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Build not found'
            }, {
                status: 404
            });
        }
        console.log('Found build:', {
            id: build.id,
            project_id: build.project_id,
            filename: build.filename,
            is_current: build.is_current,
            status: build.status
        });
        // Check if build is already current
        if (build.is_current) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'This build is already active'
            }, {
                status: 400
            });
        }
        // Set this build as current (triggers will handle the rest)
        console.log('Updating build:', buildId, 'in project:', projectId);
        // Use regular client for build update
        console.log('Using regular client for build update');
        const { data: exists, error: existsError } = await supabase.from('builds').select('*').eq('id', buildId).eq('project_id', projectId);
        console.log('Build exists check:', {
            exists,
            existsError,
            buildId,
            projectId
        });
        if (existsError) {
            console.error('Error checking if build exists:', existsError);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Failed to verify build exists',
                details: existsError.message
            }, {
                status: 500
            });
        }
        if (!exists || exists.length === 0) {
            console.error('Build not found:', {
                buildId,
                projectId
            });
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Build not found'
            }, {
                status: 404
            });
        }
        // DON'T deactivate current build yet - wait for StreamPixel confirmation
        // Just set this build to processing status (but NOT current)
        const { data: updatedBuilds, error: updateError } = await supabase.from('builds').update({
            is_current: false,
            status: 'processing',
            updated_at: new Date().toISOString()
        }).eq('id', buildId).eq('project_id', projectId).select();
        if (updateError) {
            console.error('Error activating build:', updateError);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Failed to activate build',
                details: updateError.message
            }, {
                status: 500
            });
        }
        console.log('Updated builds:', updatedBuilds);
        if (!updatedBuilds || updatedBuilds.length === 0) {
            console.error('No builds were updated. Build might not exist or user might not have permission.');
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Build not found or permission denied'
            }, {
                status: 404
            });
        }
        const updatedBuild = updatedBuilds[0];
        console.log('Build updated successfully:', updatedBuild);
        // Call StreamPixel upload API
        try {
            const streamPixelResponse = await fetch(`${request.url.split('/api')[0]}/api/streampixel/upload`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Cookie': request.headers.get('Cookie') || ''
                },
                body: JSON.stringify({
                    buildId: buildId,
                    projectId: projectId
                })
            });
            if (!streamPixelResponse.ok) {
                const errorData = await streamPixelResponse.json();
                console.error('StreamPixel upload failed:', errorData);
                // Update build with error but still return success for activation
                await supabase.from('builds').update({
                    error_message: errorData.error || 'StreamPixel upload failed',
                    updated_at: new Date().toISOString()
                }).eq('id', buildId);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    message: 'Build activated successfully, but StreamPixel upload failed',
                    build: updatedBuild,
                    streamPixelError: errorData.error
                });
            }
            const streamPixelData = await streamPixelResponse.json();
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                message: 'Build activated and uploaded to StreamPixel successfully',
                build: updatedBuild,
                streamPixelResponse: streamPixelData
            });
        } catch (streamPixelError) {
            console.error('Error calling StreamPixel API:', streamPixelError);
            // Update build with error but still return success for activation
            const errorMessage = streamPixelError instanceof Error ? streamPixelError.message : 'Unknown error';
            await supabase.from('builds').update({
                error_message: 'Failed to upload to StreamPixel: ' + errorMessage,
                updated_at: new Date().toISOString()
            }).eq('id', buildId);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                message: 'Build activated successfully, but StreamPixel upload failed',
                build: updatedBuild,
                streamPixelError: errorMessage
            });
        }
    } catch (error) {
        console.error('Error activating build:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__8badd946._.js.map