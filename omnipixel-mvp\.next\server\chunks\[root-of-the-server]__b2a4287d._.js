module.exports = {

"[project]/.next-internal/server/app/api/upload/uppy/complete/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@aws-sdk/client-s3 [external] (@aws-sdk/client-s3, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@aws-sdk/client-s3", () => require("@aws-sdk/client-s3"));

module.exports = mod;
}}),
"[externals]/@aws-sdk/s3-request-presigner [external] (@aws-sdk/s3-request-presigner, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@aws-sdk/s3-request-presigner", () => require("@aws-sdk/s3-request-presigner"));

module.exports = mod;
}}),
"[project]/lib/backblaze-adapter.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "createBackblazeS3Client": ()=>createBackblazeS3Client,
    "default": ()=>__TURBOPACK__default__export__,
    "getBackblazeBucketName": ()=>getBackblazeBucketName,
    "getBackblazePublicUrl": ()=>getBackblazePublicUrl,
    "isBackblazeConfigured": ()=>isBackblazeConfigured
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@aws-sdk/client-s3 [external] (@aws-sdk/client-s3, cjs)");
;
function createBackblazeS3Client() {
    console.log('🔧 Creating Backblaze S3 Client...');
    // Get Backblaze credentials from environment
    const BACKBLAZE_APP_KEY_ID = process.env.BACKBLAZE_APP_KEY_ID;
    const BACKBLAZE_APP_KEY = process.env.BACKBLAZE_APP_KEY;
    console.log('Credentials check:');
    console.log('- BACKBLAZE_APP_KEY_ID:', BACKBLAZE_APP_KEY_ID ? `${BACKBLAZE_APP_KEY_ID.substring(0, 15)}...` : 'MISSING');
    console.log('- BACKBLAZE_APP_KEY:', BACKBLAZE_APP_KEY ? `${BACKBLAZE_APP_KEY.substring(0, 15)}...` : 'MISSING');
    // Backblaze endpoint - use the correct region for your bucket
    // See: https://www.backblaze.com/docs/cloud-storage-s3-compatible-api-endpoint-regions
    const BACKBLAZE_ENDPOINT = 'https://s3.us-east-005.backblazeb2.com';
    console.log('Configuration:');
    console.log('- Endpoint:', BACKBLAZE_ENDPOINT);
    console.log('- Region: us-east-005');
    console.log('- Force Path Style: true');
    if (!BACKBLAZE_APP_KEY_ID || !BACKBLAZE_APP_KEY) {
        console.error('❌ Backblaze credentials missing!');
        throw new Error('Backblaze credentials not configured');
    }
    // Create S3 client with Backblaze configuration
    const client = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["S3Client"]({
        region: 'us-east-005',
        endpoint: BACKBLAZE_ENDPOINT,
        credentials: {
            accessKeyId: BACKBLAZE_APP_KEY_ID,
            secretAccessKey: BACKBLAZE_APP_KEY
        },
        forcePathStyle: true
    });
    console.log('✅ Backblaze S3 Client created successfully');
    return client;
}
function getBackblazeBucketName() {
    return process.env.BACKBLAZE_BUCKET_NAME || 'omnipixel';
}
function isBackblazeConfigured() {
    return !!(process.env.BACKBLAZE_APP_KEY_ID && process.env.BACKBLAZE_APP_KEY && process.env.BACKBLAZE_BUCKET_NAME);
}
function getBackblazePublicUrl(key) {
    const bucketName = getBackblazeBucketName();
    // For us-east-005 region, use the correct download URL format
    return `https://f005.backblazeb2.com/file/${bucketName}/${key}`;
}
// Export adapter functions
const backblazeAdapter = {
    createBackblazeS3Client,
    getBackblazeBucketName,
    isBackblazeConfigured,
    getBackblazePublicUrl
};
const __TURBOPACK__default__export__ = backblazeAdapter;
}),
"[project]/lib/aws.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "BUCKET_NAME": ()=>BUCKET_NAME,
    "abortMultipartUpload": ()=>abortMultipartUpload,
    "checkS3ObjectExists": ()=>checkS3ObjectExists,
    "completeMultipartUpload": ()=>completeMultipartUpload,
    "createMultipartUpload": ()=>createMultipartUpload,
    "default": ()=>__TURBOPACK__default__export__,
    "deleteFileFromS3": ()=>deleteFileFromS3,
    "extractS3KeyFromUrl": ()=>extractS3KeyFromUrl,
    "generatePresignedDownloadUrl": ()=>generatePresignedDownloadUrl,
    "generatePresignedUploadUrl": ()=>generatePresignedUploadUrl,
    "generateS3Key": ()=>generateS3Key,
    "getContentTypeFromFilename": ()=>getContentTypeFromFilename,
    "getPublicS3FileUrl": ()=>getPublicS3FileUrl,
    "getS3FileUrl": ()=>getS3FileUrl,
    "isAWSConfigured": ()=>isAWSConfigured,
    "makeS3ObjectPublic": ()=>makeS3ObjectPublic,
    "s3Client": ()=>s3Client,
    "uploadFileToS3": ()=>uploadFileToS3,
    "validateFileType": ()=>validateFileType
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@aws-sdk/client-s3 [external] (@aws-sdk/client-s3, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$s3$2d$request$2d$presigner__$5b$external$5d$__$2840$aws$2d$sdk$2f$s3$2d$request$2d$presigner$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@aws-sdk/s3-request-presigner [external] (@aws-sdk/s3-request-presigner, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/backblaze-adapter.ts [app-route] (ecmascript)");
;
;
;
// Use Backblaze B2 with S3-compatible API instead of AWS S3
console.log('🔧 Initializing Backblaze S3 Client...');
console.log('Environment check:');
console.log('- BACKBLAZE_APP_KEY_ID:', process.env.BACKBLAZE_APP_KEY_ID ? `${process.env.BACKBLAZE_APP_KEY_ID.substring(0, 10)}...` : 'MISSING');
console.log('- BACKBLAZE_APP_KEY:', process.env.BACKBLAZE_APP_KEY ? `${process.env.BACKBLAZE_APP_KEY.substring(0, 10)}...` : 'MISSING');
console.log('- BACKBLAZE_BUCKET_NAME:', process.env.BACKBLAZE_BUCKET_NAME || 'MISSING');
const s3Client = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createBackblazeS3Client"])();
console.log('✅ Backblaze S3 Client created successfully');
const BUCKET_NAME = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getBackblazeBucketName"])();
function generateS3Key(userId, projectId, filename) {
    const timestamp = Date.now();
    const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_');
    // No omnipixel prefix needed since bucket is already named omnipixel
    return `uploads/${userId}/${projectId}/${timestamp}_${sanitizedFilename}`;
}
async function uploadFileToS3(key, file, contentType = 'application/octet-stream', metadata) {
    console.log('🚀 Starting uploadFileToS3...');
    console.log('Parameters:');
    console.log('- key:', key);
    console.log('- file size:', file.length, 'bytes');
    console.log('- contentType:', contentType);
    console.log('- metadata:', metadata);
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBackblazeConfigured"])()) {
        console.error('❌ Backblaze not configured!');
        console.error('Environment variables:');
        console.error('- BACKBLAZE_APP_KEY_ID:', process.env.BACKBLAZE_APP_KEY_ID ? 'SET' : 'MISSING');
        console.error('- BACKBLAZE_APP_KEY:', process.env.BACKBLAZE_APP_KEY ? 'SET' : 'MISSING');
        console.error('- BACKBLAZE_BUCKET_NAME:', process.env.BACKBLAZE_BUCKET_NAME ? 'SET' : 'MISSING');
        throw new Error('Backblaze credentials not configured');
    }
    console.log('✅ Backblaze configuration check passed');
    // Prepare S3 metadata (source of truth)
    const s3Metadata = {};
    if (metadata) {
        if (metadata.projectId) s3Metadata['project-id'] = metadata.projectId;
        if (metadata.userId) s3Metadata['user-id'] = metadata.userId;
        if (metadata.filename) s3Metadata['filename'] = metadata.filename;
        if (metadata.version) s3Metadata['version'] = metadata.version;
        if (metadata.streamPixelProcess !== undefined) {
            s3Metadata['streampixel-process'] = metadata.streamPixelProcess ? 'true' : 'false';
        }
        s3Metadata['uploaded-at'] = new Date().toISOString();
    }
    console.log('📦 Creating PutObjectCommand with:');
    console.log('- Bucket:', BUCKET_NAME);
    console.log('- Key:', key);
    console.log('- ContentType:', contentType);
    console.log('- Metadata:', s3Metadata);
    const command = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["PutObjectCommand"]({
        Bucket: BUCKET_NAME,
        Key: key,
        Body: file,
        ContentType: contentType,
        Metadata: Object.keys(s3Metadata).length > 0 ? s3Metadata : undefined
    });
    console.log('📡 Sending command to Backblaze B2...');
    console.log('S3 Client config:', {
        region: s3Client.config.region,
        endpoint: s3Client.config.endpoint
    });
    try {
        const result = await s3Client.send(command);
        console.log('✅ Upload successful! Result:', result);
        console.log('File uploaded to Backblaze B2 with metadata:', s3Metadata);
        const publicUrl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getBackblazePublicUrl"])(key);
        console.log('🔗 Generated public URL:', publicUrl);
        return publicUrl;
    } catch (error) {
        console.error('❌ Upload failed with error:', error);
        // Type-safe error handling
        const errorDetails = {};
        if (error && typeof error === 'object') {
            const errorObj = error;
            errorDetails.name = errorObj.name;
            errorDetails.message = errorObj.message;
            errorDetails.code = errorObj.Code;
            errorDetails.statusCode = errorObj.$metadata?.httpStatusCode;
            errorDetails.requestId = errorObj.$metadata?.requestId;
            errorDetails.fault = errorObj.$fault;
            errorDetails.region = errorObj.$metadata?.region;
        }
        console.error('Error details:', errorDetails);
        // Additional debugging for AccessDenied errors
        if (errorDetails.code === 'AccessDenied') {
            console.error('🔍 AccessDenied debugging:');
            console.error('- Check if App Key has writeFiles permission');
            console.error('- Check if App Key is scoped to the correct bucket');
            console.error('- Verify bucket name matches exactly');
            console.error('- Current bucket name:', BUCKET_NAME);
            console.error('- App Key ID:', process.env.BACKBLAZE_APP_KEY_ID?.substring(0, 10) + '...');
        }
        throw error;
    }
}
async function generatePresignedUploadUrl(key, contentType, uploadId, partNumber, expiresIn = 3600 // 1 hour
) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBackblazeConfigured"])()) {
        throw new Error('Backblaze credentials not configured');
    }
    // For multipart upload parts
    if (uploadId && partNumber) {
        const command = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["UploadPartCommand"]({
            Bucket: BUCKET_NAME,
            Key: key,
            UploadId: uploadId,
            PartNumber: partNumber
        });
        return await (0, __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$s3$2d$request$2d$presigner__$5b$external$5d$__$2840$aws$2d$sdk$2f$s3$2d$request$2d$presigner$2c$__cjs$29$__["getSignedUrl"])(s3Client, command, {
            expiresIn
        });
    }
    // For single file upload
    const command = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["PutObjectCommand"]({
        Bucket: BUCKET_NAME,
        Key: key,
        ContentType: contentType
    });
    return await (0, __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$s3$2d$request$2d$presigner__$5b$external$5d$__$2840$aws$2d$sdk$2f$s3$2d$request$2d$presigner$2c$__cjs$29$__["getSignedUrl"])(s3Client, command, {
        expiresIn
    });
}
async function generatePresignedDownloadUrl(key, expiresIn = 3600 // 1 hour
) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBackblazeConfigured"])()) {
        throw new Error('Backblaze credentials not configured');
    }
    const command = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["GetObjectCommand"]({
        Bucket: BUCKET_NAME,
        Key: key
    });
    return await (0, __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$s3$2d$request$2d$presigner__$5b$external$5d$__$2840$aws$2d$sdk$2f$s3$2d$request$2d$presigner$2c$__cjs$29$__["getSignedUrl"])(s3Client, command, {
        expiresIn
    });
}
async function deleteFileFromS3(key) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBackblazeConfigured"])()) {
        throw new Error('Backblaze credentials not configured');
    }
    const command = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["DeleteObjectCommand"]({
        Bucket: BUCKET_NAME,
        Key: key
    });
    await s3Client.send(command);
}
async function createMultipartUpload(key, contentType = 'application/octet-stream') {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBackblazeConfigured"])()) {
        throw new Error('Backblaze credentials not configured');
    }
    const command = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["CreateMultipartUploadCommand"]({
        Bucket: BUCKET_NAME,
        Key: key,
        ContentType: contentType
    });
    const response = await s3Client.send(command);
    if (!response.UploadId) {
        throw new Error('Failed to create multipart upload');
    }
    return {
        uploadId: response.UploadId
    };
}
async function completeMultipartUpload(key, uploadId, parts) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBackblazeConfigured"])()) {
        throw new Error('Backblaze credentials not configured');
    }
    const command = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["CompleteMultipartUploadCommand"]({
        Bucket: BUCKET_NAME,
        Key: key,
        UploadId: uploadId,
        MultipartUpload: {
            Parts: parts.sort((a, b)=>a.PartNumber - b.PartNumber)
        }
    });
    const response = await s3Client.send(command);
    return response.Location || (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getBackblazePublicUrl"])(key);
}
async function abortMultipartUpload(key, uploadId) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBackblazeConfigured"])()) {
        throw new Error('Backblaze credentials not configured');
    }
    const command = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["AbortMultipartUploadCommand"]({
        Bucket: BUCKET_NAME,
        Key: key,
        UploadId: uploadId
    });
    await s3Client.send(command);
}
function isAWSConfigured() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBackblazeConfigured"])();
}
function getS3FileUrl(key) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getBackblazePublicUrl"])(key);
}
function getPublicS3FileUrl(key) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$backblaze$2d$adapter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getBackblazePublicUrl"])(key);
}
async function makeS3ObjectPublic(key) {
    if (!isAWSConfigured()) {
        throw new Error('Backblaze is not configured');
    }
    try {
        // First check if the object exists and is not a delete marker
        try {
            await s3Client.send(new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["GetObjectCommand"]({
                Bucket: BUCKET_NAME,
                Key: key
            }));
        } catch (headError) {
            if (headError && typeof headError === 'object' && 'name' in headError && headError.name === 'NoSuchKey') {
                throw new Error(`File not found in Backblaze B2: ${key}`);
            }
            if (headError && typeof headError === 'object' && 'name' in headError && headError.name === 'MethodNotAllowed') {
                throw new Error(`File is a delete marker (deleted file): ${key}`);
            }
            throw headError;
        }
        // If object exists, set ACL to public-read
        await s3Client.send(new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["PutObjectAclCommand"]({
            Bucket: BUCKET_NAME,
            Key: key,
            ACL: 'public-read'
        }));
        console.log(`Made Backblaze B2 object public: ${key}`);
    } catch (error) {
        console.error('Error making Backblaze B2 object public:', error);
        // Provide more specific error messages
        if (error && typeof error === 'object' && 'name' in error && 'ResourceType' in error && error.name === 'MethodNotAllowed' && error.ResourceType === 'DeleteMarker') {
            throw new Error(`Cannot make file public: File was deleted and only a delete marker exists. Please re-upload the file.`);
        }
        throw error;
    }
}
async function checkS3ObjectExists(key) {
    if (!isAWSConfigured()) {
        throw new Error('Backblaze is not configured');
    }
    try {
        // List object versions to check for delete markers
        const versionsResponse = await s3Client.send(new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["ListObjectVersionsCommand"]({
            Bucket: BUCKET_NAME,
            Prefix: key,
            MaxKeys: 10
        }));
        const versions = versionsResponse.Versions || [];
        const deleteMarkers = versionsResponse.DeleteMarkers || [];
        // Check if there's a current version (not deleted)
        const currentVersion = versions.find((v)=>v.Key === key && v.IsLatest);
        const currentDeleteMarker = deleteMarkers.find((dm)=>dm.Key === key && dm.IsLatest);
        return {
            exists: !!currentVersion && !currentDeleteMarker,
            isDeleteMarker: !!currentDeleteMarker,
            versions: [
                ...versions,
                ...deleteMarkers
            ]
        };
    } catch (error) {
        console.error('Error checking S3 object:', error);
        return {
            exists: false,
            isDeleteMarker: false
        };
    }
}
function extractS3KeyFromUrl(url) {
    const match = url.match(/https:\/\/[^\/]+\.s3\.[^\/]+\.amazonaws\.com\/(.+)/);
    return match ? match[1] : null;
}
function validateFileType(filename, allowedTypes = []) {
    const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    const defaultAllowedTypes = [
        '.zip',
        '.exe',
        '.apk',
        '.ipa',
        '.dmg',
        '.pkg',
        '.deb',
        '.rpm'
    ];
    const typesToCheck = allowedTypes.length > 0 ? allowedTypes : defaultAllowedTypes;
    return typesToCheck.includes(extension);
}
function getContentTypeFromFilename(filename) {
    const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    const contentTypes = {
        '.zip': 'application/zip',
        '.rar': 'application/x-rar-compressed',
        '.7z': 'application/x-7z-compressed',
        '.tar.gz': 'application/gzip',
        '.exe': 'application/x-msdownload',
        '.app': 'application/octet-stream',
        '.dmg': 'application/x-apple-diskimage',
        '.deb': 'application/vnd.debian.binary-package',
        '.rpm': 'application/x-rpm',
        '.apk': 'application/vnd.android.package-archive',
        '.ipa': 'application/octet-stream',
        '.aab': 'application/octet-stream',
        '.unity3d': 'application/octet-stream',
        '.unitypackage': 'application/octet-stream'
    };
    return contentTypes[extension] || 'application/octet-stream';
}
const awsLib = {
    s3Client,
    BUCKET_NAME,
    generateS3Key,
    uploadFileToS3,
    generatePresignedUploadUrl,
    generatePresignedDownloadUrl,
    deleteFileFromS3,
    createMultipartUpload,
    completeMultipartUpload,
    abortMultipartUpload,
    isAWSConfigured,
    getS3FileUrl,
    getPublicS3FileUrl,
    makeS3ObjectPublic,
    checkS3ObjectExists,
    extractS3KeyFromUrl,
    validateFileType,
    getContentTypeFromFilename
};
const __TURBOPACK__default__export__ = awsLib;
}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/utils/supabase/server.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "createClient": ()=>createClient
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
;
;
async function createClient(url, key) {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    url = url ?? ("TURBOPACK compile-time value", "https://qrnstvofnizsgdlubtbt.supabase.co");
    key = key ?? ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFybnN0dm9mbml6c2dkbHVidGJ0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyNzE1MDMsImV4cCI6MjA2ODg0NzUwM30.D0KmXra-FWDKBlJUvlDOmDcIXdC1mquCUA1vQFfpFVU");
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createServerClient"])(url, key, {
        cookies: {
            getAll () {
                return cookieStore.getAll();
            },
            setAll (cookiesToSet) {
                try {
                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));
                } catch  {
                // The `setAll` method was called from a Server Component.
                // This can be ignored if you have middleware refreshing
                // user sessions.
                }
            }
        }
    });
}
}),
"[project]/app/api/upload/uppy/complete/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$aws$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/aws.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        // Check if AWS is configured
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$aws$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isAWSConfigured"])()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File upload is not configured. Please contact the administrator.'
            }, {
                status: 503
            });
        }
        // Create Supabase client for server-side auth
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])();
        // Get the current user
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        // Parse request body
        const body = await request.json();
        const { filename: originalFilename, projectId, s3Key: receivedS3Key, fileSize } = body;
        // Debug: Log the received S3 key
        console.log('🔍 Received S3 key:', receivedS3Key);
        // Handle case where s3Key might be just the filename (fallback from upload handler)
        let s3Key = receivedS3Key;
        let s3Filename = '';
        let buildId = '';
        if (receivedS3Key.includes('/')) {
            // Full S3 key provided
            s3Filename = receivedS3Key.split('/').pop() || '';
            buildId = s3Filename.replace('.zip', '');
            console.log('🔍 Using full S3 key:', s3Key);
        } else {
            // Only filename provided, reconstruct full S3 key
            s3Filename = receivedS3Key;
            buildId = s3Filename.replace('.zip', '');
            s3Key = `uploads/${user.id}/${projectId}/${s3Filename}`;
            console.log('🔍 Reconstructed full S3 key:', s3Key);
        }
        console.log('🔍 Final S3 key:', s3Key);
        console.log('🔍 Extracted filename:', s3Filename);
        console.log('🔍 Extracted build ID:', buildId);
        // Validate required fields
        if (!originalFilename || !projectId || !s3Key || !buildId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Missing required fields: filename, projectId, s3Key, or invalid S3 key format'
            }, {
                status: 400
            });
        }
        // Verify that the project belongs to the user and get auto_release setting
        const { data: project, error: projectError } = await supabase.from('projects').select('id, user_id, name, auto_release').eq('id', projectId).eq('user_id', user.id).single();
        if (projectError || !project) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Project not found or access denied'
            }, {
                status: 404
            });
        }
        // Check build limits - prevent upload if user already has 2 builds
        const { data: existingBuilds, error: buildsError } = await supabase.from('builds').select('id, status').eq('project_id', projectId).not('status', 'in', '(failed,archived)');
        if (buildsError) {
            console.error('Error checking existing builds:', buildsError);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Failed to check existing builds'
            }, {
                status: 500
            });
        }
        if (existingBuilds && existingBuilds.length >= 2) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Build limit reached',
                message: 'You can have a maximum of 2 builds per project. Please delete an existing build before uploading a new one.'
            }, {
                status: 400
            });
        }
        // Get the latest version number for this project
        const { data: latestBuild } = await supabase.from('builds').select('version').eq('project_id', projectId).order('version', {
            ascending: false
        }).limit(1).single();
        const nextVersion = latestBuild ? latestBuild.version + 1 : 1;
        // Use the build ID and filename from the upload params (already uploaded to S3)
        // The file is already stored as [build-id].zip in S3
        // Determine initial status based on auto_release setting only
        let initialStatus = 'inactive';
        let isCurrent = false;
        // Check if this is the first build for the project
        const isFirstBuild = existingBuilds.length === 0;
        console.log('🔍 Auto-release enabled:', project.auto_release);
        console.log('🔍 Is first build:', isFirstBuild);
        if (project.auto_release) {
            // Only trigger StreamPixel upload if auto-release is explicitly enabled
            // Set to processing since it will be sent to StreamPixel
            // BUT don't set as current until StreamPixel webhook confirms
            initialStatus = 'processing';
            isCurrent = false; // Wait for StreamPixel webhook to set this
        // DON'T deactivate existing current build yet - wait for StreamPixel confirmation
        }
        // Store metadata in S3 (source of truth) - this will trigger Lambda processing
        try {
            // console.log('🏷️ Storing S3 metadata for:', s3Key)
            // console.log('Using bucket:', BUCKET_NAME)
            // Prepare S3 tags (metadata)
            // const s3Tags = [
            //   { Key: 'project-id', Value: projectId },
            //   { Key: 'user-id', Value: user.id },
            //   { Key: 'filename', Value: s3Filename || `${buildId}.zip` }, // Build ID based filename (e.g., uuid.zip)
            //   { Key: 'original-filename', Value: originalFilename }, // Keep original filename for reference
            //   { Key: 'build-id', Value: buildId },
            //   { Key: 'version', Value: nextVersion.toString() },
            //   { Key: 'file-size', Value: (fileSize || 0).toString() },
            //   { Key: 'status', Value: initialStatus },
            //   { Key: 'streampixel-status', Value: 'ready' },
            //   { Key: 'auto-release', Value: project.auto_release.toString() },
            //   { Key: 'is-current', Value: isCurrent.toString() },
            //   { Key: 'uploaded-at', Value: new Date().toISOString() }
            // ]
            // console.log('S3 tags to apply:', s3Tags)
            // Store metadata in S3 tags
            // await s3Client.send(new PutObjectTaggingCommand({
            //   Bucket: BUCKET_NAME,
            //   Key: s3Key,
            //   Tagging: {
            //     TagSet: s3Tags
            //   }
            // }))
            // console.log('✅ S3 metadata stored successfully for:', s3Key)
            // Create database record as cache (Lambda will also do this, but we do it here for immediate UI response)
            const { data: build, error: buildError } = await supabase.from('builds').insert({
                id: buildId,
                project_id: projectId,
                filename: s3Filename || `${buildId}.zip`,
                original_filename: originalFilename,
                s3_key: s3Key,
                version: nextVersion,
                file_size: fileSize || 0,
                status: initialStatus,
                streampixel_status: 'ready',
                is_current: isCurrent
            }).select().single();
            if (buildError) {
                console.error('Error creating build record:', buildError);
                // Don't fail the request - S3 is source of truth, database is just cache
                console.log('Database cache creation failed, but S3 metadata is stored');
            }
            // If auto-release is enabled or first build, trigger StreamPixel upload
            if (initialStatus === 'processing') {
                try {
                    console.log('🚀 Auto-triggering StreamPixel upload for build:', buildId);
                    const streamPixelResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/streampixel/upload`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Cookie': request.headers.get('Cookie') || ''
                        },
                        body: JSON.stringify({
                            buildId: buildId,
                            projectId: projectId
                        })
                    });
                    if (!streamPixelResponse.ok) {
                        console.error('StreamPixel auto-upload failed:', await streamPixelResponse.text());
                    // Don't fail the upload, just log the error
                    } else {
                        console.log('✅ StreamPixel auto-upload triggered successfully');
                    }
                } catch (streamPixelError) {
                    console.error('Error triggering StreamPixel upload:', streamPixelError);
                // Don't fail the upload, just log the error
                }
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                message: 'Upload completed successfully',
                build: build || {
                    id: buildId,
                    project_id: projectId,
                    filename: s3Filename || `${buildId}.zip`,
                    original_filename: originalFilename,
                    s3_key: s3Key,
                    version: nextVersion,
                    file_size: fileSize || 0,
                    status: initialStatus,
                    streampixel_status: 'ready',
                    is_current: isCurrent
                }
            });
        } catch (s3Error) {
            console.error('❌ Error storing S3 metadata:', s3Error);
            const errorDetails = {};
            if (s3Error && typeof s3Error === 'object') {
                const name = s3Error.name;
                errorDetails.name = typeof name === 'string' ? name : undefined;
                errorDetails.message = s3Error.message;
                errorDetails.code = s3Error.Code;
                errorDetails.statusCode = s3Error.$metadata?.httpStatusCode;
            }
            console.error('S3 Error details:', errorDetails);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Failed to store file metadata',
                details: errorDetails.message || 'Unknown S3 error'
            }, {
                status: 500
            });
        }
    // Note: StreamPixel upload is auto-triggered only for auto-release enabled projects
    } catch (error) {
        console.error('Error completing upload:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__b2a4287d._.js.map