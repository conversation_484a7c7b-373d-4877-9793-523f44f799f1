{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/lib/backblaze-adapter.ts"], "sourcesContent": ["import { S3Client } from '@aws-sdk/client-s3'\n\n// Backblaze B2 S3-Compatible Configuration\n// See: https://www.backblaze.com/docs/cloud-storage-use-the-aws-sdk-for-javascript-v3-with-backblaze-b2\n\n// Create S3-compatible client for Backblaze B2\nexport function createBackblazeS3Client(): S3Client {\n  console.log('🔧 Creating Backblaze S3 Client...')\n\n  // Get Backblaze credentials from environment\n  const BACKBLAZE_APP_KEY_ID = process.env.BACKBLAZE_APP_KEY_ID\n  const BACKBLAZE_APP_KEY = process.env.BACKBLAZE_APP_KEY\n\n  console.log('Credentials check:')\n  console.log('- BACKBLAZE_APP_KEY_ID:', BACKBLAZE_APP_KEY_ID ? `${BACKBLAZE_APP_KEY_ID.substring(0, 15)}...` : 'MISSING')\n  console.log('- BACKBLAZE_APP_KEY:', BACKBLAZE_APP_KEY ? `${BACKBLAZE_APP_KEY.substring(0, 15)}...` : 'MISSING')\n\n  // Backblaze endpoint - use the correct region for your bucket\n  // See: https://www.backblaze.com/docs/cloud-storage-s3-compatible-api-endpoint-regions\n  const BACKBLAZE_ENDPOINT = 'https://s3.us-east-005.backblazeb2.com'\n\n  console.log('Configuration:')\n  console.log('- Endpoint:', BACKBLAZE_ENDPOINT)\n  console.log('- Region: us-east-005')\n  console.log('- Force Path Style: true')\n\n  if (!BACKBLAZE_APP_KEY_ID || !BACKBLAZE_APP_KEY) {\n    console.error('❌ Backblaze credentials missing!')\n    throw new Error('Backblaze credentials not configured')\n  }\n\n  // Create S3 client with Backblaze configuration\n  const client = new S3Client({\n    region: 'us-east-005', // Backblaze region (updated to match your endpoint)\n    endpoint: BACKBLAZE_ENDPOINT,\n    credentials: {\n      accessKeyId: BACKBLAZE_APP_KEY_ID,\n      secretAccessKey: BACKBLAZE_APP_KEY,\n    },\n    forcePathStyle: true, // Required for Backblaze B2\n  })\n\n  console.log('✅ Backblaze S3 Client created successfully')\n  return client\n}\n\n// Get Backblaze bucket name\nexport function getBackblazeBucketName(): string {\n  return process.env.BACKBLAZE_BUCKET_NAME || 'omnipixel'\n}\n\n// Check if Backblaze is configured\nexport function isBackblazeConfigured(): boolean {\n  return !!(\n    process.env.BACKBLAZE_APP_KEY_ID && \n    process.env.BACKBLAZE_APP_KEY && \n    process.env.BACKBLAZE_BUCKET_NAME\n  )\n}\n\n// Get public URL for Backblaze file\nexport function getBackblazePublicUrl(key: string): string {\n  const bucketName = getBackblazeBucketName()\n  // For us-east-005 region, use the correct download URL format\n  return `https://f005.backblazeb2.com/file/${bucketName}/${key}`\n}\n\n// Export adapter functions\nconst backblazeAdapter = {\n  createBackblazeS3Client,\n  getBackblazeBucketName,\n  isBackblazeConfigured,\n  getBackblazePublicUrl,\n}\n\nexport default backblazeAdapter\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAMO,SAAS;IACd,QAAQ,GAAG,CAAC;IAEZ,6CAA6C;IAC7C,MAAM,uBAAuB,QAAQ,GAAG,CAAC,oBAAoB;IAC7D,MAAM,oBAAoB,QAAQ,GAAG,CAAC,iBAAiB;IAEvD,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,2BAA2B,uBAAuB,GAAG,qBAAqB,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;IAC9G,QAAQ,GAAG,CAAC,wBAAwB,oBAAoB,GAAG,kBAAkB,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;IAErG,8DAA8D;IAC9D,uFAAuF;IACvF,MAAM,qBAAqB;IAE3B,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,eAAe;IAC3B,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC;IAEZ,IAAI,CAAC,wBAAwB,CAAC,mBAAmB;QAC/C,QAAQ,KAAK,CAAC;QACd,MAAM,IAAI,MAAM;IAClB;IAEA,gDAAgD;IAChD,MAAM,SAAS,IAAI,iJAAA,CAAA,WAAQ,CAAC;QAC1B,QAAQ;QACR,UAAU;QACV,aAAa;YACX,aAAa;YACb,iBAAiB;QACnB;QACA,gBAAgB;IAClB;IAEA,QAAQ,GAAG,CAAC;IACZ,OAAO;AACT;AAGO,SAAS;IACd,OAAO,QAAQ,GAAG,CAAC,qBAAqB,IAAI;AAC9C;AAGO,SAAS;IACd,OAAO,CAAC,CAAC,CACP,QAAQ,GAAG,CAAC,oBAAoB,IAChC,QAAQ,GAAG,CAAC,iBAAiB,IAC7B,QAAQ,GAAG,CAAC,qBAAqB,AACnC;AACF;AAGO,SAAS,sBAAsB,GAAW;IAC/C,MAAM,aAAa;IACnB,8DAA8D;IAC9D,OAAO,CAAC,kCAAkC,EAAE,WAAW,CAAC,EAAE,KAAK;AACjE;AAEA,2BAA2B;AAC3B,MAAM,mBAAmB;IACvB;IACA;IACA;IACA;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/lib/aws.ts"], "sourcesContent": ["import {\r\n  S3<PERSON>lient,\r\n  PutObjectCommand,\r\n  PutObjectAclCommand,\r\n  GetObjectCommand,\r\n  HeadObjectCommand,\r\n  DeleteObjectCommand,\r\n  ListObjectVersionsCommand,\r\n  CreateMultipartUploadCommand,\r\n  CompleteMultipartUploadCommand,\r\n  AbortMultipartUploadCommand,\r\n  UploadPartCommand\r\n} from '@aws-sdk/client-s3'\r\nimport { getSignedUrl } from '@aws-sdk/s3-request-presigner'\r\nimport {\r\n  createBackblazeS3Client,\r\n  getBackblazeBucketName,\r\n  isBackblazeConfigured,\r\n  getBackblazePublicUrl\r\n} from './backblaze-adapter'\r\n\r\n// Use Backblaze B2 with S3-compatible API instead of AWS S3\r\nconsole.log('🔧 Initializing Backblaze S3 Client...')\r\nconsole.log('Environment check:')\r\nconsole.log('- BACKBLAZE_APP_KEY_ID:', process.env.BACKBLAZE_APP_KEY_ID ? `${process.env.BACKBLAZE_APP_KEY_ID.substring(0, 10)}...` : 'MISSING')\r\nconsole.log('- BACKBLAZE_APP_KEY:', process.env.BACKBLAZE_APP_KEY ? `${process.env.BACKBLAZE_APP_KEY.substring(0, 10)}...` : 'MISSING')\r\nconsole.log('- BACKBLAZE_BUCKET_NAME:', process.env.BACKBLAZE_BUCKET_NAME || 'MISSING')\r\n\r\nexport const s3Client = createBackblazeS3Client()\r\nconsole.log('✅ Backblaze S3 Client created successfully')\r\n\r\n// Backblaze B2 bucket name\r\nexport const BUCKET_NAME = getBackblazeBucketName()\r\n\r\n// Generate a unique S3 key for a file (without omnipixel prefix since bucket is already named omnipixel)\r\nexport function generateS3Key(userId: string, projectId: string, filename: string): string {\r\n  const timestamp = Date.now()\r\n  const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_')\r\n  // No omnipixel prefix needed since bucket is already named omnipixel\r\n  return `uploads/${userId}/${projectId}/${timestamp}_${sanitizedFilename}`\r\n}\r\n\r\n// Upload file to S3 with metadata (S3 as source of truth)\r\nexport async function uploadFileToS3(\r\n  key: string,\r\n  file: Buffer | Uint8Array | string,\r\n  contentType: string = 'application/octet-stream',\r\n  metadata?: {\r\n    projectId?: string\r\n    userId?: string\r\n    filename?: string\r\n    version?: string\r\n    streamPixelProcess?: boolean\r\n  }\r\n): Promise<string> {\r\n  console.log('🚀 Starting uploadFileToS3...')\r\n  console.log('Parameters:')\r\n  console.log('- key:', key)\r\n  console.log('- file size:', file.length, 'bytes')\r\n  console.log('- contentType:', contentType)\r\n  console.log('- metadata:', metadata)\r\n\r\n  if (!isBackblazeConfigured()) {\r\n    console.error('❌ Backblaze not configured!')\r\n    console.error('Environment variables:')\r\n    console.error('- BACKBLAZE_APP_KEY_ID:', process.env.BACKBLAZE_APP_KEY_ID ? 'SET' : 'MISSING')\r\n    console.error('- BACKBLAZE_APP_KEY:', process.env.BACKBLAZE_APP_KEY ? 'SET' : 'MISSING')\r\n    console.error('- BACKBLAZE_BUCKET_NAME:', process.env.BACKBLAZE_BUCKET_NAME ? 'SET' : 'MISSING')\r\n    throw new Error('Backblaze credentials not configured')\r\n  }\r\n  console.log('✅ Backblaze configuration check passed')\r\n\r\n  // Prepare S3 metadata (source of truth)\r\n  const s3Metadata: Record<string, string> = {}\r\n  if (metadata) {\r\n    if (metadata.projectId) s3Metadata['project-id'] = metadata.projectId\r\n    if (metadata.userId) s3Metadata['user-id'] = metadata.userId\r\n    if (metadata.filename) s3Metadata['filename'] = metadata.filename\r\n    if (metadata.version) s3Metadata['version'] = metadata.version\r\n    if (metadata.streamPixelProcess !== undefined) {\r\n      s3Metadata['streampixel-process'] = metadata.streamPixelProcess ? 'true' : 'false'\r\n    }\r\n    s3Metadata['uploaded-at'] = new Date().toISOString()\r\n  }\r\n\r\n  console.log('📦 Creating PutObjectCommand with:')\r\n  console.log('- Bucket:', BUCKET_NAME)\r\n  console.log('- Key:', key)\r\n  console.log('- ContentType:', contentType)\r\n  console.log('- Metadata:', s3Metadata)\r\n\r\n  const command = new PutObjectCommand({\r\n    Bucket: BUCKET_NAME,\r\n    Key: key,\r\n    Body: file,\r\n    ContentType: contentType,\r\n    Metadata: Object.keys(s3Metadata).length > 0 ? s3Metadata : undefined,\r\n  })\r\n\r\n  console.log('📡 Sending command to Backblaze B2...')\r\n  console.log('S3 Client config:', {\r\n    region: s3Client.config.region,\r\n    endpoint: s3Client.config.endpoint\r\n  })\r\n\r\n  try {\r\n    const result = await s3Client.send(command)\r\n    console.log('✅ Upload successful! Result:', result)\r\n    console.log('File uploaded to Backblaze B2 with metadata:', s3Metadata)\r\n\r\n    const publicUrl = getBackblazePublicUrl(key)\r\n    console.log('🔗 Generated public URL:', publicUrl)\r\n    return publicUrl\r\n  } catch (error: unknown) {\r\n    console.error('❌ Upload failed with error:', error)\r\n\r\n    // Type-safe error handling\r\n    const errorDetails: Record<string, unknown> = {}\r\n    if (error && typeof error === 'object') {\r\n      const errorObj = error as Error & {\r\n        Code?: string;\r\n        $metadata?: {\r\n          httpStatusCode?: number;\r\n          requestId?: string;\r\n          region?: string;\r\n        };\r\n        $fault?: string;\r\n      }\r\n      errorDetails.name = errorObj.name\r\n      errorDetails.message = errorObj.message\r\n      errorDetails.code = errorObj.Code\r\n      errorDetails.statusCode = errorObj.$metadata?.httpStatusCode\r\n      errorDetails.requestId = errorObj.$metadata?.requestId\r\n      errorDetails.fault = errorObj.$fault\r\n      errorDetails.region = errorObj.$metadata?.region\r\n    }\r\n    console.error('Error details:', errorDetails)\r\n\r\n    // Additional debugging for AccessDenied errors\r\n    if (errorDetails.code === 'AccessDenied') {\r\n      console.error('🔍 AccessDenied debugging:')\r\n      console.error('- Check if App Key has writeFiles permission')\r\n      console.error('- Check if App Key is scoped to the correct bucket')\r\n      console.error('- Verify bucket name matches exactly')\r\n      console.error('- Current bucket name:', BUCKET_NAME)\r\n      console.error('- App Key ID:', process.env.BACKBLAZE_APP_KEY_ID?.substring(0, 10) + '...')\r\n    }\r\n\r\n    throw error\r\n  }\r\n}\r\n\r\n// Generate presigned URL for file upload\r\nexport async function generatePresignedUploadUrl(\r\n  key: string,\r\n  contentType: string,\r\n  uploadId?: string,\r\n  partNumber?: number,\r\n  expiresIn: number = 3600 // 1 hour\r\n): Promise<string> {\r\n  if (!isBackblazeConfigured()) {\r\n    throw new Error('Backblaze credentials not configured')\r\n  }\r\n\r\n  // For multipart upload parts\r\n  if (uploadId && partNumber) {\r\n    const command = new UploadPartCommand({\r\n      Bucket: BUCKET_NAME,\r\n      Key: key,\r\n      UploadId: uploadId,\r\n      PartNumber: partNumber,\r\n    })\r\n    return await getSignedUrl(s3Client, command, { expiresIn })\r\n  }\r\n\r\n  // For single file upload\r\n  const command = new PutObjectCommand({\r\n    Bucket: BUCKET_NAME,\r\n    Key: key,\r\n    ContentType: contentType,\r\n  })\r\n\r\n  return await getSignedUrl(s3Client, command, { expiresIn })\r\n}\r\n\r\n// Generate presigned URL for file download\r\nexport async function generatePresignedDownloadUrl(\r\n  key: string,\r\n  expiresIn: number = 3600 // 1 hour\r\n): Promise<string> {\r\n  if (!isBackblazeConfigured()) {\r\n    throw new Error('Backblaze credentials not configured')\r\n  }\r\n\r\n  const command = new GetObjectCommand({\r\n    Bucket: BUCKET_NAME,\r\n    Key: key,\r\n  })\r\n\r\n  return await getSignedUrl(s3Client, command, { expiresIn })\r\n}\r\n\r\n// Delete file from S3\r\nexport async function deleteFileFromS3(key: string): Promise<void> {\r\n  if (!isBackblazeConfigured()) {\r\n    throw new Error('Backblaze credentials not configured')\r\n  }\r\n\r\n  const command = new DeleteObjectCommand({\r\n    Bucket: BUCKET_NAME,\r\n    Key: key,\r\n  })\r\n\r\n  await s3Client.send(command)\r\n}\r\n\r\n// Create multipart upload\r\nexport async function createMultipartUpload(\r\n  key: string,\r\n  contentType: string = 'application/octet-stream'\r\n): Promise<{ uploadId: string }> {\r\n  if (!isBackblazeConfigured()) {\r\n    throw new Error('Backblaze credentials not configured')\r\n  }\r\n\r\n  const command = new CreateMultipartUploadCommand({\r\n    Bucket: BUCKET_NAME,\r\n    Key: key,\r\n    ContentType: contentType,\r\n  })\r\n\r\n  const response = await s3Client.send(command)\r\n\r\n  if (!response.UploadId) {\r\n    throw new Error('Failed to create multipart upload')\r\n  }\r\n\r\n  return { uploadId: response.UploadId }\r\n}\r\n\r\n// Complete multipart upload\r\nexport async function completeMultipartUpload(\r\n  key: string,\r\n  uploadId: string,\r\n  parts: Array<{ PartNumber: number; ETag: string }>\r\n): Promise<string> {\r\n  if (!isBackblazeConfigured()) {\r\n    throw new Error('Backblaze credentials not configured')\r\n  }\r\n\r\n  const command = new CompleteMultipartUploadCommand({\r\n    Bucket: BUCKET_NAME,\r\n    Key: key,\r\n    UploadId: uploadId,\r\n    MultipartUpload: {\r\n      Parts: parts.sort((a, b) => a.PartNumber - b.PartNumber),\r\n    },\r\n  })\r\n\r\n  const response = await s3Client.send(command)\r\n  return response.Location || getBackblazePublicUrl(key)\r\n}\r\n\r\n// Abort multipart upload\r\nexport async function abortMultipartUpload(\r\n  key: string,\r\n  uploadId: string\r\n): Promise<void> {\r\n  if (!isBackblazeConfigured()) {\r\n    throw new Error('Backblaze credentials not configured')\r\n  }\r\n\r\n  const command = new AbortMultipartUploadCommand({\r\n    Bucket: BUCKET_NAME,\r\n    Key: key,\r\n    UploadId: uploadId,\r\n  })\r\n\r\n  await s3Client.send(command)\r\n}\r\n\r\n// Check if Backblaze is configured (keeping function name for compatibility)\r\nexport function isAWSConfigured(): boolean {\r\n  return isBackblazeConfigured()\r\n}\r\n\r\n// Get Backblaze file URL (public)\r\nexport function getS3FileUrl(key: string): string {\r\n  return getBackblazePublicUrl(key)\r\n}\r\n\r\n// Get public Backblaze file URL (for public buckets)\r\nexport function getPublicS3FileUrl(key: string): string {\r\n  return getBackblazePublicUrl(key)\r\n}\r\n\r\n// Make a specific S3 object public\r\nexport async function makeS3ObjectPublic(key: string): Promise<void> {\r\n  if (!isAWSConfigured()) {\r\n    throw new Error('Backblaze is not configured')\r\n  }\r\n\r\n  try {\r\n    // First check if the object exists and is not a delete marker\r\n    try {\r\n      await s3Client.send(new GetObjectCommand({\r\n        Bucket: BUCKET_NAME,\r\n        Key: key\r\n      }))\r\n    } catch (headError: unknown) {\r\n      if (headError && typeof headError === 'object' && 'name' in headError && headError.name === 'NoSuchKey') {\r\n        throw new Error(`File not found in Backblaze B2: ${key}`)\r\n      }\r\n      if (headError && typeof headError === 'object' && 'name' in headError && headError.name === 'MethodNotAllowed') {\r\n        throw new Error(`File is a delete marker (deleted file): ${key}`)\r\n      }\r\n      throw headError\r\n    }\r\n\r\n    // If object exists, set ACL to public-read\r\n    await s3Client.send(new PutObjectAclCommand({\r\n      Bucket: BUCKET_NAME,\r\n      Key: key,\r\n      ACL: 'public-read'\r\n    }))\r\n    console.log(`Made Backblaze B2 object public: ${key}`)\r\n  } catch (error: unknown) {\r\n    console.error('Error making Backblaze B2 object public:', error)\r\n\r\n    // Provide more specific error messages\r\n    if (error && typeof error === 'object' && 'name' in error && 'ResourceType' in error &&\r\n        error.name === 'MethodNotAllowed' && error.ResourceType === 'DeleteMarker') {\r\n      throw new Error(`Cannot make file public: File was deleted and only a delete marker exists. Please re-upload the file.`)\r\n    }\r\n\r\n    throw error\r\n  }\r\n}\r\n\r\n// Check if S3 object exists and is not a delete marker\r\nexport async function checkS3ObjectExists(key: string): Promise<{ exists: boolean; isDeleteMarker: boolean; versions?: unknown[] }> {\r\n  if (!isAWSConfigured()) {\r\n    throw new Error('Backblaze is not configured')\r\n  }\r\n\r\n  try {\r\n    // List object versions to check for delete markers\r\n    const versionsResponse = await s3Client.send(new ListObjectVersionsCommand({\r\n      Bucket: BUCKET_NAME,\r\n      Prefix: key,\r\n      MaxKeys: 10\r\n    }))\r\n\r\n    const versions = versionsResponse.Versions || []\r\n    const deleteMarkers = versionsResponse.DeleteMarkers || []\r\n\r\n    // Check if there's a current version (not deleted)\r\n    const currentVersion = versions.find(v => v.Key === key && v.IsLatest)\r\n    const currentDeleteMarker = deleteMarkers.find(dm => dm.Key === key && dm.IsLatest)\r\n\r\n    return {\r\n      exists: !!currentVersion && !currentDeleteMarker,\r\n      isDeleteMarker: !!currentDeleteMarker,\r\n      versions: [...versions, ...deleteMarkers]\r\n    }\r\n  } catch (error) {\r\n    console.error('Error checking S3 object:', error)\r\n    return { exists: false, isDeleteMarker: false }\r\n  }\r\n}\r\n\r\n// Extract S3 key from URL\r\nexport function extractS3KeyFromUrl(url: string): string | null {\r\n  const match = url.match(/https:\\/\\/[^\\/]+\\.s3\\.[^\\/]+\\.amazonaws\\.com\\/(.+)/)\r\n  return match ? match[1] : null\r\n}\r\n\r\n// Validate file type for uploads\r\nexport function validateFileType(filename: string, allowedTypes: string[] = []): boolean {\r\n  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'))\r\n  const defaultAllowedTypes = ['.zip', '.exe', '.apk', '.ipa', '.dmg', '.pkg', '.deb', '.rpm']\r\n  const typesToCheck = allowedTypes.length > 0 ? allowedTypes : defaultAllowedTypes\r\n  return typesToCheck.includes(extension)\r\n}\r\n\r\n// Get content type from filename\r\nexport function getContentTypeFromFilename(filename: string): string {\r\n  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'))\r\n  \r\n  const contentTypes: Record<string, string> = {\r\n    '.zip': 'application/zip',\r\n    '.rar': 'application/x-rar-compressed',\r\n    '.7z': 'application/x-7z-compressed',\r\n    '.tar.gz': 'application/gzip',\r\n    '.exe': 'application/x-msdownload',\r\n    '.app': 'application/octet-stream',\r\n    '.dmg': 'application/x-apple-diskimage',\r\n    '.deb': 'application/vnd.debian.binary-package',\r\n    '.rpm': 'application/x-rpm',\r\n    '.apk': 'application/vnd.android.package-archive',\r\n    '.ipa': 'application/octet-stream',\r\n    '.aab': 'application/octet-stream',\r\n    '.unity3d': 'application/octet-stream',\r\n    '.unitypackage': 'application/octet-stream',\r\n  }\r\n\r\n  return contentTypes[extension] || 'application/octet-stream'\r\n}\r\n\r\nconst awsLib = {\r\n  s3Client,\r\n  BUCKET_NAME,\r\n  generateS3Key,\r\n  uploadFileToS3,\r\n  generatePresignedUploadUrl,\r\n  generatePresignedDownloadUrl,\r\n  deleteFileFromS3,\r\n  createMultipartUpload,\r\n  completeMultipartUpload,\r\n  abortMultipartUpload,\r\n  isAWSConfigured,\r\n  getS3FileUrl,\r\n  getPublicS3FileUrl,\r\n  makeS3ObjectPublic,\r\n  checkS3ObjectExists,\r\n  extractS3KeyFromUrl,\r\n  validateFileType,\r\n  getContentTypeFromFilename,\r\n}\r\n\r\nexport default awsLib\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAaA;AACA;;;;AAOA,4DAA4D;AAC5D,QAAQ,GAAG,CAAC;AACZ,QAAQ,GAAG,CAAC;AACZ,QAAQ,GAAG,CAAC,2BAA2B,QAAQ,GAAG,CAAC,oBAAoB,GAAG,GAAG,QAAQ,GAAG,CAAC,oBAAoB,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AACtI,QAAQ,GAAG,CAAC,wBAAwB,QAAQ,GAAG,CAAC,iBAAiB,GAAG,GAAG,QAAQ,GAAG,CAAC,iBAAiB,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAC7H,QAAQ,GAAG,CAAC,4BAA4B,QAAQ,GAAG,CAAC,qBAAqB,IAAI;AAEtE,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,0BAAuB,AAAD;AAC9C,QAAQ,GAAG,CAAC;AAGL,MAAM,cAAc,CAAA,GAAA,6HAAA,CAAA,yBAAsB,AAAD;AAGzC,SAAS,cAAc,MAAc,EAAE,SAAiB,EAAE,QAAgB;IAC/E,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,oBAAoB,SAAS,OAAO,CAAC,mBAAmB;IAC9D,qEAAqE;IACrE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,UAAU,CAAC,EAAE,UAAU,CAAC,EAAE,mBAAmB;AAC3E;AAGO,eAAe,eACpB,GAAW,EACX,IAAkC,EAClC,cAAsB,0BAA0B,EAChD,QAMC;IAED,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,UAAU;IACtB,QAAQ,GAAG,CAAC,gBAAgB,KAAK,MAAM,EAAE;IACzC,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,QAAQ,GAAG,CAAC,eAAe;IAE3B,IAAI,CAAC,CAAA,GAAA,6HAAA,CAAA,wBAAqB,AAAD,KAAK;QAC5B,QAAQ,KAAK,CAAC;QACd,QAAQ,KAAK,CAAC;QACd,QAAQ,KAAK,CAAC,2BAA2B,QAAQ,GAAG,CAAC,oBAAoB,GAAG,QAAQ;QACpF,QAAQ,KAAK,CAAC,wBAAwB,QAAQ,GAAG,CAAC,iBAAiB,GAAG,QAAQ;QAC9E,QAAQ,KAAK,CAAC,4BAA4B,QAAQ,GAAG,CAAC,qBAAqB,GAAG,QAAQ;QACtF,MAAM,IAAI,MAAM;IAClB;IACA,QAAQ,GAAG,CAAC;IAEZ,wCAAwC;IACxC,MAAM,aAAqC,CAAC;IAC5C,IAAI,UAAU;QACZ,IAAI,SAAS,SAAS,EAAE,UAAU,CAAC,aAAa,GAAG,SAAS,SAAS;QACrE,IAAI,SAAS,MAAM,EAAE,UAAU,CAAC,UAAU,GAAG,SAAS,MAAM;QAC5D,IAAI,SAAS,QAAQ,EAAE,UAAU,CAAC,WAAW,GAAG,SAAS,QAAQ;QACjE,IAAI,SAAS,OAAO,EAAE,UAAU,CAAC,UAAU,GAAG,SAAS,OAAO;QAC9D,IAAI,SAAS,kBAAkB,KAAK,WAAW;YAC7C,UAAU,CAAC,sBAAsB,GAAG,SAAS,kBAAkB,GAAG,SAAS;QAC7E;QACA,UAAU,CAAC,cAAc,GAAG,IAAI,OAAO,WAAW;IACpD;IAEA,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,aAAa;IACzB,QAAQ,GAAG,CAAC,UAAU;IACtB,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,QAAQ,GAAG,CAAC,eAAe;IAE3B,MAAM,UAAU,IAAI,iJAAA,CAAA,mBAAgB,CAAC;QACnC,QAAQ;QACR,KAAK;QACL,MAAM;QACN,aAAa;QACb,UAAU,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,IAAI,aAAa;IAC9D;IAEA,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,qBAAqB;QAC/B,QAAQ,SAAS,MAAM,CAAC,MAAM;QAC9B,UAAU,SAAS,MAAM,CAAC,QAAQ;IACpC;IAEA,IAAI;QACF,MAAM,SAAS,MAAM,SAAS,IAAI,CAAC;QACnC,QAAQ,GAAG,CAAC,gCAAgC;QAC5C,QAAQ,GAAG,CAAC,gDAAgD;QAE5D,MAAM,YAAY,CAAA,GAAA,6HAAA,CAAA,wBAAqB,AAAD,EAAE;QACxC,QAAQ,GAAG,CAAC,4BAA4B;QACxC,OAAO;IACT,EAAE,OAAO,OAAgB;QACvB,QAAQ,KAAK,CAAC,+BAA+B;QAE7C,2BAA2B;QAC3B,MAAM,eAAwC,CAAC;QAC/C,IAAI,SAAS,OAAO,UAAU,UAAU;YACtC,MAAM,WAAW;YASjB,aAAa,IAAI,GAAG,SAAS,IAAI;YACjC,aAAa,OAAO,GAAG,SAAS,OAAO;YACvC,aAAa,IAAI,GAAG,SAAS,IAAI;YACjC,aAAa,UAAU,GAAG,SAAS,SAAS,EAAE;YAC9C,aAAa,SAAS,GAAG,SAAS,SAAS,EAAE;YAC7C,aAAa,KAAK,GAAG,SAAS,MAAM;YACpC,aAAa,MAAM,GAAG,SAAS,SAAS,EAAE;QAC5C;QACA,QAAQ,KAAK,CAAC,kBAAkB;QAEhC,+CAA+C;QAC/C,IAAI,aAAa,IAAI,KAAK,gBAAgB;YACxC,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,QAAQ,KAAK,CAAC,iBAAiB,QAAQ,GAAG,CAAC,oBAAoB,EAAE,UAAU,GAAG,MAAM;QACtF;QAEA,MAAM;IACR;AACF;AAGO,eAAe,2BACpB,GAAW,EACX,WAAmB,EACnB,QAAiB,EACjB,UAAmB,EACnB,YAAoB,KAAK,SAAS;AAAV;IAExB,IAAI,CAAC,CAAA,GAAA,6HAAA,CAAA,wBAAqB,AAAD,KAAK;QAC5B,MAAM,IAAI,MAAM;IAClB;IAEA,6BAA6B;IAC7B,IAAI,YAAY,YAAY;QAC1B,MAAM,UAAU,IAAI,iJAAA,CAAA,oBAAiB,CAAC;YACpC,QAAQ;YACR,KAAK;YACL,UAAU;YACV,YAAY;QACd;QACA,OAAO,MAAM,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE,UAAU,SAAS;YAAE;QAAU;IAC3D;IAEA,yBAAyB;IACzB,MAAM,UAAU,IAAI,iJAAA,CAAA,mBAAgB,CAAC;QACnC,QAAQ;QACR,KAAK;QACL,aAAa;IACf;IAEA,OAAO,MAAM,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE,UAAU,SAAS;QAAE;IAAU;AAC3D;AAGO,eAAe,6BACpB,GAAW,EACX,YAAoB,KAAK,SAAS;AAAV;IAExB,IAAI,CAAC,CAAA,GAAA,6HAAA,CAAA,wBAAqB,AAAD,KAAK;QAC5B,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,UAAU,IAAI,iJAAA,CAAA,mBAAgB,CAAC;QACnC,QAAQ;QACR,KAAK;IACP;IAEA,OAAO,MAAM,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE,UAAU,SAAS;QAAE;IAAU;AAC3D;AAGO,eAAe,iBAAiB,GAAW;IAChD,IAAI,CAAC,CAAA,GAAA,6HAAA,CAAA,wBAAqB,AAAD,KAAK;QAC5B,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,UAAU,IAAI,iJAAA,CAAA,sBAAmB,CAAC;QACtC,QAAQ;QACR,KAAK;IACP;IAEA,MAAM,SAAS,IAAI,CAAC;AACtB;AAGO,eAAe,sBACpB,GAAW,EACX,cAAsB,0BAA0B;IAEhD,IAAI,CAAC,CAAA,GAAA,6HAAA,CAAA,wBAAqB,AAAD,KAAK;QAC5B,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,UAAU,IAAI,iJAAA,CAAA,+BAA4B,CAAC;QAC/C,QAAQ;QACR,KAAK;QACL,aAAa;IACf;IAEA,MAAM,WAAW,MAAM,SAAS,IAAI,CAAC;IAErC,IAAI,CAAC,SAAS,QAAQ,EAAE;QACtB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;QAAE,UAAU,SAAS,QAAQ;IAAC;AACvC;AAGO,eAAe,wBACpB,GAAW,EACX,QAAgB,EAChB,KAAkD;IAElD,IAAI,CAAC,CAAA,GAAA,6HAAA,CAAA,wBAAqB,AAAD,KAAK;QAC5B,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,UAAU,IAAI,iJAAA,CAAA,iCAA8B,CAAC;QACjD,QAAQ;QACR,KAAK;QACL,UAAU;QACV,iBAAiB;YACf,OAAO,MAAM,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;QACzD;IACF;IAEA,MAAM,WAAW,MAAM,SAAS,IAAI,CAAC;IACrC,OAAO,SAAS,QAAQ,IAAI,CAAA,GAAA,6HAAA,CAAA,wBAAqB,AAAD,EAAE;AACpD;AAGO,eAAe,qBACpB,GAAW,EACX,QAAgB;IAEhB,IAAI,CAAC,CAAA,GAAA,6HAAA,CAAA,wBAAqB,AAAD,KAAK;QAC5B,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,UAAU,IAAI,iJAAA,CAAA,8BAA2B,CAAC;QAC9C,QAAQ;QACR,KAAK;QACL,UAAU;IACZ;IAEA,MAAM,SAAS,IAAI,CAAC;AACtB;AAGO,SAAS;IACd,OAAO,CAAA,GAAA,6HAAA,CAAA,wBAAqB,AAAD;AAC7B;AAGO,SAAS,aAAa,GAAW;IACtC,OAAO,CAAA,GAAA,6HAAA,CAAA,wBAAqB,AAAD,EAAE;AAC/B;AAGO,SAAS,mBAAmB,GAAW;IAC5C,OAAO,CAAA,GAAA,6HAAA,CAAA,wBAAqB,AAAD,EAAE;AAC/B;AAGO,eAAe,mBAAmB,GAAW;IAClD,IAAI,CAAC,mBAAmB;QACtB,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,8DAA8D;QAC9D,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,IAAI,iJAAA,CAAA,mBAAgB,CAAC;gBACvC,QAAQ;gBACR,KAAK;YACP;QACF,EAAE,OAAO,WAAoB;YAC3B,IAAI,aAAa,OAAO,cAAc,YAAY,UAAU,aAAa,UAAU,IAAI,KAAK,aAAa;gBACvG,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,KAAK;YAC1D;YACA,IAAI,aAAa,OAAO,cAAc,YAAY,UAAU,aAAa,UAAU,IAAI,KAAK,oBAAoB;gBAC9G,MAAM,IAAI,MAAM,CAAC,wCAAwC,EAAE,KAAK;YAClE;YACA,MAAM;QACR;QAEA,2CAA2C;QAC3C,MAAM,SAAS,IAAI,CAAC,IAAI,iJAAA,CAAA,sBAAmB,CAAC;YAC1C,QAAQ;YACR,KAAK;YACL,KAAK;QACP;QACA,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,KAAK;IACvD,EAAE,OAAO,OAAgB;QACvB,QAAQ,KAAK,CAAC,4CAA4C;QAE1D,uCAAuC;QACvC,IAAI,SAAS,OAAO,UAAU,YAAY,UAAU,SAAS,kBAAkB,SAC3E,MAAM,IAAI,KAAK,sBAAsB,MAAM,YAAY,KAAK,gBAAgB;YAC9E,MAAM,IAAI,MAAM,CAAC,qGAAqG,CAAC;QACzH;QAEA,MAAM;IACR;AACF;AAGO,eAAe,oBAAoB,GAAW;IACnD,IAAI,CAAC,mBAAmB;QACtB,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,mDAAmD;QACnD,MAAM,mBAAmB,MAAM,SAAS,IAAI,CAAC,IAAI,iJAAA,CAAA,4BAAyB,CAAC;YACzE,QAAQ;YACR,QAAQ;YACR,SAAS;QACX;QAEA,MAAM,WAAW,iBAAiB,QAAQ,IAAI,EAAE;QAChD,MAAM,gBAAgB,iBAAiB,aAAa,IAAI,EAAE;QAE1D,mDAAmD;QACnD,MAAM,iBAAiB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,OAAO,EAAE,QAAQ;QACrE,MAAM,sBAAsB,cAAc,IAAI,CAAC,CAAA,KAAM,GAAG,GAAG,KAAK,OAAO,GAAG,QAAQ;QAElF,OAAO;YACL,QAAQ,CAAC,CAAC,kBAAkB,CAAC;YAC7B,gBAAgB,CAAC,CAAC;YAClB,UAAU;mBAAI;mBAAa;aAAc;QAC3C;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YAAE,QAAQ;YAAO,gBAAgB;QAAM;IAChD;AACF;AAGO,SAAS,oBAAoB,GAAW;IAC7C,MAAM,QAAQ,IAAI,KAAK,CAAC;IACxB,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;AAC5B;AAGO,SAAS,iBAAiB,QAAgB,EAAE,eAAyB,EAAE;IAC5E,MAAM,YAAY,SAAS,WAAW,GAAG,SAAS,CAAC,SAAS,WAAW,CAAC;IACxE,MAAM,sBAAsB;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IAC5F,MAAM,eAAe,aAAa,MAAM,GAAG,IAAI,eAAe;IAC9D,OAAO,aAAa,QAAQ,CAAC;AAC/B;AAGO,SAAS,2BAA2B,QAAgB;IACzD,MAAM,YAAY,SAAS,WAAW,GAAG,SAAS,CAAC,SAAS,WAAW,CAAC;IAExE,MAAM,eAAuC;QAC3C,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,WAAW;QACX,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,iBAAiB;IACnB;IAEA,OAAO,YAAY,CAAC,UAAU,IAAI;AACpC;AAEA,MAAM,SAAS;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 589, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\r\nimport { cookies } from 'next/headers'\r\n\r\nexport async function createClient(url?:string, key?:string) {\r\n  const cookieStore = await cookies()\r\n  url = url ?? process.env.NEXT_PUBLIC_SUPABASE_URL!\r\n  key = key ?? process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY!\r\n\r\n  return createServerClient(\r\n    url,\r\n    key,\r\n    {\r\n      cookies: {\r\n        getAll() {\r\n          return cookieStore.getAll()\r\n        },\r\n        setAll(cookiesToSet) {\r\n          try {\r\n            cookiesToSet.forEach(({ name, value, options }) =>\r\n              cookieStore.set(name, value, options)\r\n            )\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  )\r\n}"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe,aAAa,GAAW,EAAE,GAAW;IACzD,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,MAAM;IACN,MAAM;IAEN,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EACtB,KACA,KACA;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 622, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/app/api/upload/uppy/complete/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\r\nimport { s3Client, BUCKET_NAME, isAWSConfigured } from '@/lib/aws'\r\nimport { PutObjectTaggingCommand } from '@aws-sdk/client-s3'\r\nimport { createClient } from '@/utils/supabase/server'\r\n\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    // Check if AWS is configured\r\n    if (!isAWSConfigured()) {\r\n      return NextResponse.json(\r\n        { error: 'File upload is not configured. Please contact the administrator.' },\r\n        { status: 503 }\r\n      )\r\n    }\r\n\r\n    // Create Supabase client for server-side auth\r\n    const supabase = await createClient();\r\n\r\n    // Get the current user\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\r\n\r\n    if (authError || !user) {\r\n      return NextResponse.json(\r\n        { error: 'Unauthorized' },\r\n        { status: 401 }\r\n      )\r\n    }\r\n\r\n    // Parse request body\r\n    const body = await request.json()\r\n    const { filename: originalFilename, projectId, s3Key: receivedS3Key, fileSize } = body\r\n\r\n    // Debug: Log the received S3 key\r\n    console.log('🔍 Received S3 key:', receivedS3Key)\r\n\r\n    // Handle case where s3Key might be just the filename (fallback from upload handler)\r\n    let s3Key = receivedS3Key\r\n    let s3Filename = ''\r\n    let buildId = ''\r\n\r\n    if (receivedS3Key.includes('/')) {\r\n      // Full S3 key provided\r\n      s3Filename = receivedS3Key.split('/').pop() || ''\r\n      buildId = s3Filename.replace('.zip', '')\r\n      console.log('🔍 Using full S3 key:', s3Key)\r\n    } else {\r\n      // Only filename provided, reconstruct full S3 key\r\n      s3Filename = receivedS3Key\r\n      buildId = s3Filename.replace('.zip', '')\r\n      s3Key = `uploads/${user.id}/${projectId}/${s3Filename}`\r\n      console.log('🔍 Reconstructed full S3 key:', s3Key)\r\n    }\r\n\r\n    console.log('🔍 Final S3 key:', s3Key)\r\n    console.log('🔍 Extracted filename:', s3Filename)\r\n    console.log('🔍 Extracted build ID:', buildId)\r\n\r\n    // Validate required fields\r\n    if (!originalFilename || !projectId || !s3Key || !buildId) {\r\n      return NextResponse.json(\r\n        { error: 'Missing required fields: filename, projectId, s3Key, or invalid S3 key format' },\r\n        { status: 400 }\r\n      )\r\n    }\r\n\r\n    // Verify that the project belongs to the user and get auto_release setting\r\n    const { data: project, error: projectError } = await supabase\r\n      .from('projects')\r\n      .select('id, user_id, name, auto_release')\r\n      .eq('id', projectId)\r\n      .eq('user_id', user.id)\r\n      .single()\r\n\r\n    if (projectError || !project) {\r\n      return NextResponse.json(\r\n        { error: 'Project not found or access denied' },\r\n        { status: 404 }\r\n      )\r\n    }\r\n\r\n    // Check build limits - prevent upload if user already has 2 builds\r\n    const { data: existingBuilds, error: buildsError } = await supabase\r\n      .from('builds')\r\n      .select('id, status')\r\n      .eq('project_id', projectId)\r\n      .not('status', 'in', '(failed,archived)')\r\n\r\n    if (buildsError) {\r\n      console.error('Error checking existing builds:', buildsError)\r\n      return NextResponse.json(\r\n        { error: 'Failed to check existing builds' },\r\n        { status: 500 }\r\n      )\r\n    }\r\n\r\n    if (existingBuilds && existingBuilds.length >= 2) {\r\n      return NextResponse.json(\r\n        {\r\n          error: 'Build limit reached',\r\n          message: 'You can have a maximum of 2 builds per project. Please delete an existing build before uploading a new one.'\r\n        },\r\n        { status: 400 }\r\n      )\r\n    }\r\n\r\n    // Get the latest version number for this project\r\n    const { data: latestBuild } = await supabase\r\n      .from('builds')\r\n      .select('version')\r\n      .eq('project_id', projectId)\r\n      .order('version', { ascending: false })\r\n      .limit(1)\r\n      .single()\r\n\r\n    const nextVersion = latestBuild ? latestBuild.version + 1 : 1\r\n\r\n    // Use the build ID and filename from the upload params (already uploaded to S3)\r\n    // The file is already stored as [build-id].zip in S3\r\n\r\n    // Determine initial status based on auto_release setting only\r\n    let initialStatus: 'inactive' | 'processing' = 'inactive'\r\n    let isCurrent = false\r\n\r\n    // Check if this is the first build for the project\r\n    const isFirstBuild = existingBuilds.length === 0\r\n\r\n    console.log('🔍 Auto-release enabled:', project.auto_release)\r\n    console.log('🔍 Is first build:', isFirstBuild)\r\n\r\n    if (project.auto_release) {\r\n      // Only trigger StreamPixel upload if auto-release is explicitly enabled\r\n      // Set to processing since it will be sent to StreamPixel\r\n      // BUT don't set as current until StreamPixel webhook confirms\r\n      initialStatus = 'processing'\r\n      isCurrent = false // Wait for StreamPixel webhook to set this\r\n\r\n      // DON'T deactivate existing current build yet - wait for StreamPixel confirmation\r\n    }\r\n\r\n    // Store metadata in S3 (source of truth) - this will trigger Lambda processing\r\n    try {\r\n      // console.log('🏷️ Storing S3 metadata for:', s3Key)\r\n      // console.log('Using bucket:', BUCKET_NAME)\r\n\r\n      // Prepare S3 tags (metadata)\r\n      // const s3Tags = [\r\n      //   { Key: 'project-id', Value: projectId },\r\n      //   { Key: 'user-id', Value: user.id },\r\n      //   { Key: 'filename', Value: s3Filename || `${buildId}.zip` }, // Build ID based filename (e.g., uuid.zip)\r\n      //   { Key: 'original-filename', Value: originalFilename }, // Keep original filename for reference\r\n      //   { Key: 'build-id', Value: buildId },\r\n      //   { Key: 'version', Value: nextVersion.toString() },\r\n      //   { Key: 'file-size', Value: (fileSize || 0).toString() },\r\n      //   { Key: 'status', Value: initialStatus },\r\n      //   { Key: 'streampixel-status', Value: 'ready' },\r\n      //   { Key: 'auto-release', Value: project.auto_release.toString() },\r\n      //   { Key: 'is-current', Value: isCurrent.toString() },\r\n      //   { Key: 'uploaded-at', Value: new Date().toISOString() }\r\n      // ]\r\n\r\n      // console.log('S3 tags to apply:', s3Tags)\r\n\r\n      // Store metadata in S3 tags\r\n      // await s3Client.send(new PutObjectTaggingCommand({\r\n      //   Bucket: BUCKET_NAME,\r\n      //   Key: s3Key,\r\n      //   Tagging: {\r\n      //     TagSet: s3Tags\r\n      //   }\r\n      // }))\r\n\r\n      // console.log('✅ S3 metadata stored successfully for:', s3Key)\r\n\r\n      // Create database record as cache (Lambda will also do this, but we do it here for immediate UI response)\r\n      const { data: build, error: buildError } = await supabase\r\n        .from('builds')\r\n        .insert({\r\n          id: buildId, // Use the build ID from S3 filename\r\n          project_id: projectId,\r\n          filename: s3Filename || `${buildId}.zip`, // Build ID based filename (e.g., uuid.zip)\r\n          original_filename: originalFilename, // Store original filename for reference\r\n          s3_key: s3Key,\r\n          version: nextVersion,\r\n          file_size: fileSize || 0,\r\n          status: initialStatus, // inactive by default, active if first build or auto-release\r\n          streampixel_status: 'ready',\r\n          is_current: isCurrent, // true if first build or auto-release enabled\r\n        })\r\n        .select()\r\n        .single()\r\n\r\n      if (buildError) {\r\n        console.error('Error creating build record:', buildError)\r\n        // Don't fail the request - S3 is source of truth, database is just cache\r\n        console.log('Database cache creation failed, but S3 metadata is stored')\r\n      }\r\n\r\n      // If auto-release is enabled or first build, trigger StreamPixel upload\r\n      if (initialStatus === 'processing') {\r\n        try {\r\n          console.log('🚀 Auto-triggering StreamPixel upload for build:', buildId)\r\n          const streamPixelResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/streampixel/upload`, {\r\n            method: 'POST',\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n              'Cookie': request.headers.get('Cookie') || '', // Forward cookies for auth\r\n            },\r\n            body: JSON.stringify({\r\n              buildId: buildId,\r\n              projectId: projectId\r\n            })\r\n          })\r\n\r\n          if (!streamPixelResponse.ok) {\r\n            console.error('StreamPixel auto-upload failed:', await streamPixelResponse.text())\r\n            // Don't fail the upload, just log the error\r\n          } else {\r\n            console.log('✅ StreamPixel auto-upload triggered successfully')\r\n          }\r\n        } catch (streamPixelError) {\r\n          console.error('Error triggering StreamPixel upload:', streamPixelError)\r\n          // Don't fail the upload, just log the error\r\n        }\r\n      }\r\n\r\n      return NextResponse.json({\r\n        message: 'Upload completed successfully',\r\n        build: build || {\r\n          id: buildId,\r\n          project_id: projectId,\r\n          filename: s3Filename || `${buildId}.zip`,\r\n          original_filename: originalFilename,\r\n          s3_key: s3Key,\r\n          version: nextVersion,\r\n          file_size: fileSize || 0,\r\n          status: initialStatus,\r\n          streampixel_status: 'ready',\r\n          is_current: isCurrent,\r\n        }\r\n      })\r\n\r\n    } catch (s3Error: unknown) {\r\n      console.error('❌ Error storing S3 metadata:', s3Error)\r\n\r\n      // Type-safe error handling\r\n      interface S3ErrorMetadata {\r\n              httpStatusCode?: number;\r\n            }\r\n            interface S3ErrorDetails {\r\n              name?: string;\r\n              message?: string;\r\n              code?: string;\r\n              statusCode?: number;\r\n            }\r\n            const errorDetails: S3ErrorDetails = {}\r\n            if (s3Error && typeof s3Error === 'object') {\r\n              const name = (s3Error as Record<string, unknown>).name\r\n              errorDetails.name = typeof name === 'string' ? name : undefined\r\n              errorDetails.message = (s3Error as Record<string, unknown>).message as string | undefined\r\n              errorDetails.code = (s3Error as Record<string, unknown>).Code as string | undefined\r\n              errorDetails.statusCode = (s3Error as { $metadata?: S3ErrorMetadata }).$metadata?.httpStatusCode\r\n            }\r\n      console.error('S3 Error details:', errorDetails)\r\n\r\n      return NextResponse.json(\r\n        {\r\n          error: 'Failed to store file metadata',\r\n          details: errorDetails.message || 'Unknown S3 error'\r\n        },\r\n        { status: 500 }\r\n      )\r\n    }\r\n\r\n    // Note: StreamPixel upload is auto-triggered only for auto-release enabled projects\r\n\r\n  } catch (error) {\r\n    console.error('Error completing upload:', error)\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    )\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,6BAA6B;QAC7B,IAAI,CAAC,CAAA,GAAA,4GAAA,CAAA,kBAAe,AAAD,KAAK;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmE,GAC5E;gBAAE,QAAQ;YAAI;QAElB;QAEA,8CAA8C;QAC9C,MAAM,WAAW,MAAM,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;QAElC,uBAAuB;QACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAe,GACxB;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,UAAU,gBAAgB,EAAE,SAAS,EAAE,OAAO,aAAa,EAAE,QAAQ,EAAE,GAAG;QAElF,iCAAiC;QACjC,QAAQ,GAAG,CAAC,uBAAuB;QAEnC,oFAAoF;QACpF,IAAI,QAAQ;QACZ,IAAI,aAAa;QACjB,IAAI,UAAU;QAEd,IAAI,cAAc,QAAQ,CAAC,MAAM;YAC/B,uBAAuB;YACvB,aAAa,cAAc,KAAK,CAAC,KAAK,GAAG,MAAM;YAC/C,UAAU,WAAW,OAAO,CAAC,QAAQ;YACrC,QAAQ,GAAG,CAAC,yBAAyB;QACvC,OAAO;YACL,kDAAkD;YAClD,aAAa;YACb,UAAU,WAAW,OAAO,CAAC,QAAQ;YACrC,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,YAAY;YACvD,QAAQ,GAAG,CAAC,iCAAiC;QAC/C;QAEA,QAAQ,GAAG,CAAC,oBAAoB;QAChC,QAAQ,GAAG,CAAC,0BAA0B;QACtC,QAAQ,GAAG,CAAC,0BAA0B;QAEtC,2BAA2B;QAC3B,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,SAAS,CAAC,SAAS;YACzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgF,GACzF;gBAAE,QAAQ;YAAI;QAElB;QAEA,2EAA2E;QAC3E,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,YACL,MAAM,CAAC,mCACP,EAAE,CAAC,MAAM,WACT,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,MAAM;QAET,IAAI,gBAAgB,CAAC,SAAS;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqC,GAC9C;gBAAE,QAAQ;YAAI;QAElB;QAEA,mEAAmE;QACnE,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SACxD,IAAI,CAAC,UACL,MAAM,CAAC,cACP,EAAE,CAAC,cAAc,WACjB,GAAG,CAAC,UAAU,MAAM;QAEvB,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,kBAAkB,eAAe,MAAM,IAAI,GAAG;YAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,SAAS;YACX,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,iDAAiD;QACjD,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,UACL,MAAM,CAAC,WACP,EAAE,CAAC,cAAc,WACjB,KAAK,CAAC,WAAW;YAAE,WAAW;QAAM,GACpC,KAAK,CAAC,GACN,MAAM;QAET,MAAM,cAAc,cAAc,YAAY,OAAO,GAAG,IAAI;QAE5D,gFAAgF;QAChF,qDAAqD;QAErD,8DAA8D;QAC9D,IAAI,gBAA2C;QAC/C,IAAI,YAAY;QAEhB,mDAAmD;QACnD,MAAM,eAAe,eAAe,MAAM,KAAK;QAE/C,QAAQ,GAAG,CAAC,4BAA4B,QAAQ,YAAY;QAC5D,QAAQ,GAAG,CAAC,sBAAsB;QAElC,IAAI,QAAQ,YAAY,EAAE;YACxB,wEAAwE;YACxE,yDAAyD;YACzD,8DAA8D;YAC9D,gBAAgB;YAChB,YAAY,OAAM,2CAA2C;QAE7D,kFAAkF;QACpF;QAEA,+EAA+E;QAC/E,IAAI;YACF,qDAAqD;YACrD,4CAA4C;YAE5C,6BAA6B;YAC7B,mBAAmB;YACnB,6CAA6C;YAC7C,wCAAwC;YACxC,4GAA4G;YAC5G,mGAAmG;YACnG,yCAAyC;YACzC,uDAAuD;YACvD,6DAA6D;YAC7D,6CAA6C;YAC7C,mDAAmD;YACnD,qEAAqE;YACrE,wDAAwD;YACxD,4DAA4D;YAC5D,IAAI;YAEJ,2CAA2C;YAE3C,4BAA4B;YAC5B,oDAAoD;YACpD,yBAAyB;YACzB,gBAAgB;YAChB,eAAe;YACf,qBAAqB;YACrB,MAAM;YACN,MAAM;YAEN,+DAA+D;YAE/D,0GAA0G;YAC1G,MAAM,EAAE,MAAM,KAAK,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SAC9C,IAAI,CAAC,UACL,MAAM,CAAC;gBACN,IAAI;gBACJ,YAAY;gBACZ,UAAU,cAAc,GAAG,QAAQ,IAAI,CAAC;gBACxC,mBAAmB;gBACnB,QAAQ;gBACR,SAAS;gBACT,WAAW,YAAY;gBACvB,QAAQ;gBACR,oBAAoB;gBACpB,YAAY;YACd,GACC,MAAM,GACN,MAAM;YAET,IAAI,YAAY;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,yEAAyE;gBACzE,QAAQ,GAAG,CAAC;YACd;YAEA,wEAAwE;YACxE,IAAI,kBAAkB,cAAc;gBAClC,IAAI;oBACF,QAAQ,GAAG,CAAC,oDAAoD;oBAChE,MAAM,sBAAsB,MAAM,MAAM,GAAG,QAAQ,GAAG,CAAC,mBAAmB,IAAI,wBAAwB,uBAAuB,CAAC,EAAE;wBAC9H,QAAQ;wBACR,SAAS;4BACP,gBAAgB;4BAChB,UAAU,QAAQ,OAAO,CAAC,GAAG,CAAC,aAAa;wBAC7C;wBACA,MAAM,KAAK,SAAS,CAAC;4BACnB,SAAS;4BACT,WAAW;wBACb;oBACF;oBAEA,IAAI,CAAC,oBAAoB,EAAE,EAAE;wBAC3B,QAAQ,KAAK,CAAC,mCAAmC,MAAM,oBAAoB,IAAI;oBAC/E,4CAA4C;oBAC9C,OAAO;wBACL,QAAQ,GAAG,CAAC;oBACd;gBACF,EAAE,OAAO,kBAAkB;oBACzB,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,4CAA4C;gBAC9C;YACF;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO,SAAS;oBACd,IAAI;oBACJ,YAAY;oBACZ,UAAU,cAAc,GAAG,QAAQ,IAAI,CAAC;oBACxC,mBAAmB;oBACnB,QAAQ;oBACR,SAAS;oBACT,WAAW,YAAY;oBACvB,QAAQ;oBACR,oBAAoB;oBACpB,YAAY;gBACd;YACF;QAEF,EAAE,OAAO,SAAkB;YACzB,QAAQ,KAAK,CAAC,gCAAgC;YAYxC,MAAM,eAA+B,CAAC;YACtC,IAAI,WAAW,OAAO,YAAY,UAAU;gBAC1C,MAAM,OAAO,AAAC,QAAoC,IAAI;gBACtD,aAAa,IAAI,GAAG,OAAO,SAAS,WAAW,OAAO;gBACtD,aAAa,OAAO,GAAG,AAAC,QAAoC,OAAO;gBACnE,aAAa,IAAI,GAAG,AAAC,QAAoC,IAAI;gBAC7D,aAAa,UAAU,GAAG,AAAC,QAA4C,SAAS,EAAE;YACpF;YACN,QAAQ,KAAK,CAAC,qBAAqB;YAEnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,SAAS,aAAa,OAAO,IAAI;YACnC,GACA;gBAAE,QAAQ;YAAI;QAElB;IAEA,oFAAoF;IAEtF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}