{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\r\nimport { cookies } from 'next/headers'\r\n\r\nexport async function createClient(url?:string, key?:string) {\r\n  const cookieStore = await cookies()\r\n  url = url ?? process.env.NEXT_PUBLIC_SUPABASE_URL!\r\n  key = key ?? process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY!\r\n\r\n  return createServerClient(\r\n    url,\r\n    key,\r\n    {\r\n      cookies: {\r\n        getAll() {\r\n          return cookieStore.getAll()\r\n        },\r\n        setAll(cookiesToSet) {\r\n          try {\r\n            cookiesToSet.forEach(({ name, value, options }) =>\r\n              cookieStore.set(name, value, options)\r\n            )\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  )\r\n}"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe,aAAa,GAAW,EAAE,GAAW;IACzD,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM;IACN,MAAM;IAEN,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EACtB,KACA,KACA;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/app/admin/projects/%5Bid%5D/client.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/admin/projects/[id]/client.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/admin/projects/[id]/client.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/app/admin/projects/%5Bid%5D/client.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/admin/projects/[id]/client.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/admin/projects/[id]/client.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/app/admin/projects/%5Bid%5D/page.tsx"], "sourcesContent": ["import { notFound, redirect } from 'next/navigation'\nimport { createClient } from '@/utils/supabase/server'\nimport AdminProjectDetailClient from './client'\n\nexport default async function AdminProjectDetailPage({ params }: { params: Promise<{ id: string }> }) {\n  const { id } = await params\n  const supabase = await createClient()\n  const { data: { user } } = await supabase.auth.getUser()\n  if (!user) redirect('/login')\n\n  // Fetch profile\n  const { data: profile } = await supabase\n    .from('profiles')\n    .select('*')\n    .eq('id', user.id)\n    .single()\n  if (!profile || profile.role !== 'platform_admin') {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Access Denied</h1>\n          <p className=\"text-gray-600 mt-2\">You need platform admin privileges to access this page.</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Fetch the admin project (with builds and owner email)\n  const { data: project, error } = await supabase\n    .from('projects')\n    .select(`\n      *,\n      builds (\n        id, filename, original_filename, s3_key, version, status, is_current, file_size,\n        streampixel_build_id, streampixel_status, error_message,\n        created_at, updated_at\n      ),\n      profiles (\n        email, role\n      )\n    `)\n    .eq('id', id)\n    .single()\n  if (error || !project) notFound()\n\n  return <AdminProjectDetailClient initialProject={project} />\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;;;;;AAEe,eAAe,uBAAuB,EAAE,MAAM,EAAuC;IAClG,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IACrB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,IAAI,CAAC,MAAM,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IAEpB,gBAAgB;IAChB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;IACT,IAAI,CAAC,WAAW,QAAQ,IAAI,KAAK,kBAAkB;QACjD,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,wDAAwD;IACxD,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;;;;;;;;IAUT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;IACT,IAAI,SAAS,CAAC,SAAS,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IAE9B,qBAAO,8OAAC,6IAAA,CAAA,UAAwB;QAAC,gBAAgB;;;;;;AACnD", "debugId": null}}]}