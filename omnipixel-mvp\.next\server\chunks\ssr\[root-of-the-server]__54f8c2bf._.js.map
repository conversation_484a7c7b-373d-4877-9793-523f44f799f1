{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\r\nimport { cookies } from 'next/headers'\r\n\r\nexport async function createClient(url?:string, key?:string) {\r\n  const cookieStore = await cookies()\r\n  url = url ?? process.env.NEXT_PUBLIC_SUPABASE_URL!\r\n  key = key ?? process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY!\r\n\r\n  return createServerClient(\r\n    url,\r\n    key,\r\n    {\r\n      cookies: {\r\n        getAll() {\r\n          return cookieStore.getAll()\r\n        },\r\n        setAll(cookiesToSet) {\r\n          try {\r\n            cookiesToSet.forEach(({ name, value, options }) =>\r\n              cookieStore.set(name, value, options)\r\n            )\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  )\r\n}"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe,aAAa,GAAW,EAAE,GAAW;IACzD,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM;IACN,MAAM;IAEN,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EACtB,KACA,KACA;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/app/admin/users/page.tsx"], "sourcesContent": ["// app/admin/users/page.tsx\nimport { redirect } from 'next/navigation'\nimport { createClient } from '@/utils/supabase/server'\nimport AdminUsersClient from './client'\n\nexport default async function AdminUsersPage() {\n  const supabase = await createClient()\n  // Auth\n  const { data: { user } } = await supabase.auth.getUser()\n  if (!user) redirect('/login')\n\n  // Profile\n  const { data: profile } = await supabase\n    .from('profiles')\n    .select('*')\n    .eq('id', user.id)\n    .single()\n  if (!profile || profile.role !== 'platform_admin') redirect('/admin')\n\n  // Fetch users with projects (first page, limit 20)\n  const { data: usersRaw, count } = await supabase\n    .from('profiles')\n    .select(`\n      id,\n      email,\n      full_name,\n      role,\n      created_at,\n      updated_at,\n      projects:projects (\n        id, name, created_at\n      )\n    `, { count: 'exact' })\n    .order('created_at', { ascending: false })\n    .range(0, 19)\n\n  const users = (usersRaw ?? []).map((u: any) => ({\n    id: u.id,\n    email: u.email,\n    full_name: u.full_name,\n    role: u.role,\n    created_at: u.created_at,\n    updated_at: u.updated_at,\n    projects: u.projects || [],\n  }))\n\n  // Pagination\n  const pagination = {\n    page: 1,\n    limit: 20,\n    total: count || users.length,\n    totalPages: Math.max(1, Math.ceil((count || users.length) / 20))\n  }\n\n  return (\n    <AdminUsersClient\n      user={user}\n      profile={profile}\n      initialUsers={users}\n      initialPagination={pagination}\n    />\n  )\n}\n"], "names": [], "mappings": "AAAA,2BAA2B;;;;;AAC3B;AAAA;AACA;AACA;;;;;AAEe,eAAe;IAC5B,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,OAAO;IACP,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,IAAI,CAAC,MAAM,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IAEpB,UAAU;IACV,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;IACT,IAAI,CAAC,WAAW,QAAQ,IAAI,KAAK,kBAAkB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IAE5D,mDAAmD;IACnD,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;;;;;;;;IAUT,CAAC,EAAE;QAAE,OAAO;IAAQ,GACnB,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM,GACvC,KAAK,CAAC,GAAG;IAEZ,MAAM,QAAQ,CAAC,YAAY,EAAE,EAAE,GAAG,CAAC,CAAC,IAAW,CAAC;YAC9C,IAAI,EAAE,EAAE;YACR,OAAO,EAAE,KAAK;YACd,WAAW,EAAE,SAAS;YACtB,MAAM,EAAE,IAAI;YACZ,YAAY,EAAE,UAAU;YACxB,YAAY,EAAE,UAAU;YACxB,UAAU,EAAE,QAAQ,IAAI,EAAE;QAC5B,CAAC;IAED,aAAa;IACb,MAAM,aAAa;QACjB,MAAM;QACN,OAAO;QACP,OAAO,SAAS,MAAM,MAAM;QAC5B,YAAY,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,SAAS,MAAM,MAAM,IAAI;IAC9D;IAEA,qBACE,8OAAC,gIAAA,CAAA,UAAgB;QACf,MAAM;QACN,SAAS;QACT,cAAc;QACd,mBAAmB;;;;;;AAGzB", "debugId": null}}]}