{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/lib/auth-context'\nimport { Button } from '@/components/ui/button'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { User, Settings, LogOut, Shield } from 'lucide-react'\n\nexport function Navigation() {\n  const { user, profile, signOut } = useAuth()\n  const router = useRouter()\n\n  const handleSignOut = async () => {\n    await signOut()\n    router.push('/login')\n  }\n\n  if (!user) return null\n\n  return (\n    <nav className=\"border-b bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/dashboard\" className=\"flex-shrink-0\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Omnipixel</h1>\n            </Link>\n            <div className=\"hidden md:ml-6 md:flex md:space-x-8\">\n              <Link\n                href=\"/dashboard\"\n                className=\"text-gray-900 hover:text-gray-700 px-3 py-2 text-sm font-medium\"\n              >\n                Dashboard\n              </Link>\n              {profile?.role === 'platform_admin' && (\n                <Link\n                  href=\"/admin\"\n                  className=\"text-gray-900 hover:text-gray-700 px-3 py-2 text-sm font-medium flex items-center gap-1\"\n                >\n                  <Shield className=\"h-4 w-4\" />\n                  Admin\n                </Link>\n              )}\n            </div>\n          </div>\n          <div className=\"flex items-center\">\n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                  <User className=\"h-4 w-4\" />\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                <div className=\"flex items-center justify-start gap-2 p-2\">\n                  <div className=\"flex flex-col space-y-1 leading-none\">\n                    <p className=\"font-medium\">{user.email}</p>\n                    <p className=\"text-xs text-muted-foreground\">\n                      {profile?.role === 'platform_admin' ? 'Platform Admin' : 'User'}\n                    </p>\n                  </div>\n                </div>\n                <DropdownMenuSeparator />\n                <DropdownMenuItem>\n                  <Settings className=\"mr-2 h-4 w-4\" />\n                  <span>Settings</span>\n                </DropdownMenuItem>\n                {profile?.role === 'platform_admin' && (\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/admin\">\n                      <Shield className=\"mr-2 h-4 w-4\" />\n                      <span>Admin Panel</span>\n                    </Link>\n                  </DropdownMenuItem>\n                )}\n                <DropdownMenuSeparator />\n                <DropdownMenuItem onClick={handleSignOut}>\n                  <LogOut className=\"mr-2 h-4 w-4\" />\n                  <span>Log out</span>\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAbA;;;;;;;;AAeO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACzC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;0CAChC,cAAA,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;oCAGA,SAAS,SAAS,kCACjB,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;kCAMtC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,qIAAA,CAAA,eAAY;;8CACX,8OAAC,qIAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,WAAU;kDAChC,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAGpB,8OAAC,qIAAA,CAAA,sBAAmB;oCAAC,WAAU;oCAAO,OAAM;oCAAM,UAAU;;sDAC1D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAe,KAAK,KAAK;;;;;;kEACtC,8OAAC;wDAAE,WAAU;kEACV,SAAS,SAAS,mBAAmB,mBAAmB;;;;;;;;;;;;;;;;;sDAI/D,8OAAC,qIAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,qIAAA,CAAA,mBAAgB;;8DACf,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;8DAAK;;;;;;;;;;;;wCAEP,SAAS,SAAS,kCACjB,8OAAC,qIAAA,CAAA,mBAAgB;4CAAC,OAAO;sDACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;;kEACT,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;sDAIZ,8OAAC,qIAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS;;8DACzB,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB", "debugId": null}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 736, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 773, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 817, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 880, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 908, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/lib/clear-service-workers.ts"], "sourcesContent": ["'use client'\n\n// Utility to clear all service workers that might interfere with Uppy\nexport async function clearAllServiceWorkers(): Promise<void> {\n  if (typeof navigator === 'undefined' || !('serviceWorker' in navigator)) {\n    console.log('Service workers not supported')\n    return\n  }\n\n  try {\n    const registrations = await navigator.serviceWorker.getRegistrations()\n    console.log(`Found ${registrations.length} service worker registrations`)\n    \n    for (const registration of registrations) {\n      console.log('Unregistering service worker:', registration.scope)\n      await registration.unregister()\n    }\n    \n    console.log('All service workers unregistered')\n  } catch (error) {\n    console.error('Failed to unregister service workers:', error)\n  }\n}\n\n// Call this function to clear service workers\nif (typeof window !== 'undefined') {\n  // Auto-clear on load to prevent interference with Uppy\n  clearAllServiceWorkers()\n}\n"], "names": [], "mappings": ";;;AAAA;AAGO,eAAe;IACpB,IAAI,OAAO,cAAc,eAAe,CAAC,CAAC,mBAAmB,SAAS,GAAG;QACvE,QAAQ,GAAG,CAAC;QACZ;IACF;IAEA,IAAI;QACF,MAAM,gBAAgB,MAAM,UAAU,aAAa,CAAC,gBAAgB;QACpE,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,cAAc,MAAM,CAAC,6BAA6B,CAAC;QAExE,KAAK,MAAM,gBAAgB,cAAe;YACxC,QAAQ,GAAG,CAAC,iCAAiC,aAAa,KAAK;YAC/D,MAAM,aAAa,UAAU;QAC/B;QAEA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;IACzD;AACF;AAEA,8CAA8C;AAC9C", "debugId": null}}, {"offset": {"line": 936, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/uppy-file-upload.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useEffect, useRef, useState, useCallback, useMemo } from 'react'\r\nimport Uppy, { UppyFile } from '@uppy/core'\r\nimport AwsS3 from '@uppy/aws-s3'\r\n\r\nimport { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Progress } from '@/components/ui/progress'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { Alert, AlertDescription } from '@/components/ui/alert'\r\nimport { Separator } from '@/components/ui/separator'\r\nimport {\r\n  AlertCircle,\r\n  CheckCircle,\r\n  Upload,\r\n  Pause,\r\n  Play,\r\n  X,\r\n  File,\r\n  Clock,\r\n  Zap,\r\n  FileText\r\n} from 'lucide-react'\r\nimport { clearAllServiceWorkers } from '@/lib/clear-service-workers'\r\nimport { cn } from '@/lib/utils'\r\nimport { Body, Meta, UploadResult } from '@uppy/core'\r\n\r\n\r\n// Import minimal Uppy styles\r\nimport '@uppy/core/dist/style.min.css'\r\n\r\ninterface UppyFileUploadProps {\r\n  projectId: string\r\n  onUploadComplete?: (result: UploadResult<Meta, Body>) => void\r\n  onUploadError?: (error: string) => void\r\n  className?: string\r\n}\r\n\r\nexport function UppyFileUpload({\r\n  projectId,\r\n  onUploadComplete,\r\n  onUploadError,\r\n  className\r\n}: UppyFileUploadProps) {\r\n  const uppyRef = useRef<Uppy<Meta> | null>(null)\r\n  const fileInputRef = useRef<HTMLDivElement>(null)\r\n  const hiddenFileInputRef = useRef<HTMLInputElement>(null)\r\n  const [isUploading, setIsUploading] = useState(false)\r\n  const [uploadProgress, setUploadProgress] = useState(0)\r\n  const [uploadSpeed, setUploadSpeed] = useState(0)\r\n  const [timeRemaining, setTimeRemaining] = useState(0)\r\n  const [isPaused, setIsPaused] = useState(false)\r\n  const [error, setError] = useState<string | null>(null)\r\n  const [success, setSuccess] = useState<string | null>(null)\r\n  const [selectedFile, setSelectedFile] = useState<UppyFile<Meta, Record<string, unknown>> | null>(null)\r\n  const [isDragOver, setIsDragOver] = useState(false)\r\n\r\n  useEffect(() => {\r\n    // Clean up any interfering service workers\r\n    clearAllServiceWorkers().catch(console.warn)\r\n\r\n    // Initialize Uppy\r\n    const uppy = new Uppy<Meta>({\r\n      id: 'omnipixel-uploader',\r\n      autoProceed: false,\r\n      allowMultipleUploads: false,\r\n      restrictions: {\r\n        maxFileSize: 24 * 1024 * 1024 * 1024, // 24GB\r\n        maxNumberOfFiles: 1,\r\n        allowedFileTypes: ['.zip', 'application/zip', 'application/x-zip-compressed'],\r\n      },\r\n      meta: {\r\n        projectId,\r\n      },\r\n    })\r\n\r\n    // Configure AWS S3 multipart upload\r\n    uppy.use(AwsS3, {\r\n      shouldUseMultipart: (file) => (file.size || 0) > 100 * 1024 * 1024, // Use multipart for files > 100MB\r\n      limit: 4, // Concurrent uploads\r\n      retryDelays: [0, 1000, 3000, 5000],\r\n\r\n      // For single file uploads\r\n      getUploadParameters: async (file) => {\r\n        try {\r\n          const response = await fetch('/api/upload/uppy/single-params', {\r\n            method: 'POST',\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n            body: JSON.stringify({\r\n              filename: file.name,\r\n              type: file.type,\r\n              projectId,\r\n            }),\r\n          })\r\n\r\n          if (!response.ok) {\r\n            throw new Error('Failed to get upload parameters')\r\n          }\r\n\r\n          return await response.json()\r\n        } catch (error) {\r\n          console.error('Error getting upload parameters:', error)\r\n          throw error\r\n        }\r\n      },\r\n\r\n      // For multipart uploads - create the upload\r\n      createMultipartUpload: async (file) => {\r\n        try {\r\n          const response = await fetch('/api/upload/uppy/multipart-params', {\r\n            method: 'POST',\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n            body: JSON.stringify({\r\n              filename: file.name,\r\n              type: file.type,\r\n              projectId,\r\n              fileSize: file.size,\r\n            }),\r\n          })\r\n\r\n          if (!response.ok) {\r\n            throw new Error('Failed to create multipart upload')\r\n          }\r\n\r\n          return await response.json()\r\n        } catch (error) {\r\n          console.error('Error creating multipart upload:', error)\r\n          throw error\r\n        }\r\n      },\r\n\r\n      // Sign individual parts for multipart upload\r\n      signPart: async (file, { uploadId, key, partNumber }) => {\r\n        try {\r\n          const response = await fetch('/api/upload/uppy/sign-part', {\r\n            method: 'POST',\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n            body: JSON.stringify({\r\n              key,\r\n              uploadId,\r\n              partNumber,\r\n            }),\r\n          })\r\n\r\n          if (!response.ok) {\r\n            throw new Error('Failed to sign part')\r\n          }\r\n\r\n          return await response.json()\r\n        } catch (error) {\r\n          console.error('Error signing part:', error)\r\n          throw error\r\n        }\r\n      },\r\n\r\n      // Complete multipart upload\r\n      completeMultipartUpload: async (file, { uploadId, key, parts }) => {\r\n        try {\r\n          const response = await fetch('/api/upload/uppy/complete-multipart', {\r\n            method: 'POST',\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n            body: JSON.stringify({\r\n              key,\r\n              uploadId,\r\n              parts,\r\n            }),\r\n          })\r\n\r\n          if (!response.ok) {\r\n            throw new Error('Failed to complete multipart upload')\r\n          }\r\n\r\n          return await response.json()\r\n        } catch (error) {\r\n          console.error('Error completing multipart upload:', error)\r\n          throw error\r\n        }\r\n      },\r\n\r\n      // List parts (for resuming uploads)\r\n      listParts: async (file, { uploadId, key }) => {\r\n        try {\r\n          const response = await fetch('/api/upload/uppy/list-parts', {\r\n            method: 'POST',\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n            body: JSON.stringify({\r\n              key,\r\n              uploadId,\r\n            }),\r\n          })\r\n\r\n          if (!response.ok) {\r\n            throw new Error('Failed to list parts')\r\n          }\r\n\r\n          const data = await response.json()\r\n          return data.parts || []\r\n        } catch (error) {\r\n          console.error('Error listing parts:', error)\r\n          return []\r\n        }\r\n      },\r\n\r\n      // Abort multipart upload\r\n      abortMultipartUpload: async (file, { uploadId, key }) => {\r\n        try {\r\n          await fetch('/api/upload/uppy/abort-multipart', {\r\n            method: 'POST',\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n            body: JSON.stringify({\r\n              key,\r\n              uploadId,\r\n            }),\r\n          })\r\n        } catch (error) {\r\n          console.error('Error aborting multipart upload:', error)\r\n          // Don't throw - cancellation should not fail\r\n        }\r\n      },\r\n    })\r\n\r\n    // Using custom file input instead of Uppy FileInput plugin\r\n\r\n    // Event listeners\r\n    uppy.on('file-added', (file) => {\r\n      console.log('File added to Uppy:', file.name, file)\r\n      setSelectedFile(file)\r\n      setError(null)\r\n      setSuccess(null)\r\n    })\r\n\r\n    uppy.on('file-removed', (file) => {\r\n      console.log('File removed from Uppy:', file?.name)\r\n      setSelectedFile(null)\r\n      setError(null)\r\n      setSuccess(null)\r\n    })\r\n\r\n    uppy.on('restriction-failed', (file, error) => {\r\n      console.error('File restriction failed:', file?.name, error)\r\n      setError(`File restriction failed: ${error.message}`)\r\n    })\r\n\r\n    uppy.on('error', (error) => {\r\n      console.error('Uppy error:', error)\r\n      setError(`Upload error: ${error.message}`)\r\n    })\r\n\r\n    uppy.on('upload-error', (file, error, response) => {\r\n      console.error('Upload error for file:', file?.name, error, response)\r\n      setError(`Upload failed: ${error.message}`)\r\n    })\r\n\r\n    uppy.on('upload-start', () => {\r\n      console.log('Upload started')\r\n      setIsUploading(true)\r\n      setError(null)\r\n      setSuccess(null)\r\n    })\r\n\r\n    uppy.on('upload-progress', (file, progress) => {\r\n      if (file && progress) {\r\n        const percentage = Math.round((progress.bytesUploaded / (progress.bytesTotal || 1)) * 100)\r\n        setUploadProgress(percentage)\r\n\r\n        // Calculate upload speed and time remaining\r\n        const elapsed = Date.now() - progress.uploadStarted\r\n        const speed = progress.bytesUploaded / (elapsed / 1000) // bytes per second\r\n        const remaining = ((progress.bytesTotal || 0) - progress.bytesUploaded) / speed // seconds\r\n\r\n        setUploadSpeed(speed)\r\n        setTimeRemaining(remaining)\r\n      }\r\n    })\r\n\r\n    uppy.on('upload-success', async (file, response) => {\r\n      console.log('Upload success:', file?.name)\r\n      console.log('Full response object:', JSON.stringify(response, null, 2))\r\n\r\n      try {\r\n        // Extract S3 key from response and ensure bucket name is stripped\r\n        let s3Key = ''\r\n\r\n        console.log('Processing upload response:', response)\r\n\r\n        // First, try to get the s3Key from the file's upload parameters (set during upload preparation)\r\n        if (file?.meta?.key) {\r\n          // Use the key that was set during upload preparation (contains full path)\r\n          s3Key = file.meta.key\r\n          console.log('Using s3_key from file meta:', s3Key)\r\n        } else if (response.body?.build?.s3_key) {\r\n          // Use the s3_key from our API response (this is from our complete endpoint)\r\n          s3Key = response.body.build.s3_key\r\n          console.log('Using s3_key from API response:', s3Key)\r\n        } else if (response.uploadURL && typeof response.uploadURL === 'string') {\r\n          try {\r\n            // For single uploads, extract key from URL\r\n            const url = new URL(response.uploadURL)\r\n            let path = url.pathname.substring(1) // Remove leading slash\r\n            // Remove bucket name if it's included in the path\r\n            if (path.startsWith('omnipixel/')) {\r\n              path = path.substring('omnipixel/'.length)\r\n            }\r\n            s3Key = path\r\n          } catch (urlError) {\r\n            console.warn('Invalid uploadURL format:', response.uploadURL, urlError)\r\n            // Fallback: try to extract key from the uploadURL string directly\r\n            if (response.uploadURL.includes('/')) {\r\n              const parts = response.uploadURL.split('/')\r\n              s3Key = parts[parts.length - 1] // Get the last part as filename\r\n            }\r\n          }\r\n        } else if (response.body?.key) {\r\n          // For multipart uploads, use the key directly but strip bucket name if present\r\n          let path = String(response.body.key)\r\n          if (path.startsWith('omnipixel/')) {\r\n            path = path.substring('omnipixel/'.length)\r\n          }\r\n          s3Key = path\r\n        } else if (response.body?.location) {\r\n          // Extract from location URL and remove bucket name if present\r\n          const url = new URL(response.body.location)\r\n          let path = url.pathname.substring(1) // Remove leading slash\r\n          // Remove bucket name if it's included in the path\r\n          if (path.startsWith('omnipixel/')) {\r\n            path = path.substring('omnipixel/'.length)\r\n          }\r\n          s3Key = path\r\n        }\r\n\r\n        // Final fallback: reconstruct the s3Key using the same logic as upload preparation\r\n        if (!s3Key && file?.name) {\r\n          console.warn('No s3_key found in response, attempting to reconstruct from filename:', file.name)\r\n\r\n          // Try to get the full S3 key by calling our API to get user info and reconstruct\r\n          // For now, use filename as fallback - the complete endpoint will handle this\r\n          s3Key = file.name\r\n          console.log('Using filename as s3Key fallback, complete endpoint will resolve full path')\r\n        }\r\n\r\n        console.log('Final s3Key for completion:', s3Key)\r\n\r\n        // Complete the upload on the server\r\n        const completeResponse = await fetch('/api/upload/uppy/complete', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n          },\r\n          body: JSON.stringify({\r\n            filename: file?.name,\r\n            projectId,\r\n            s3Key,\r\n            fileSize: file?.size,\r\n          }),\r\n        })\r\n\r\n        if (!completeResponse.ok) {\r\n          throw new Error('Failed to complete upload')\r\n        }\r\n\r\n        const result = await completeResponse.json()\r\n        console.log('Upload completion result:', result)\r\n        setSuccess(`Upload completed successfully: ${file?.name}`)\r\n        onUploadComplete?.(result)\r\n      } catch (error) {\r\n        console.error('Error completing upload:', error)\r\n        setError('Upload completed but failed to register. Please try again.')\r\n        onUploadError?.('Upload completed but failed to register. Please try again.')\r\n      }\r\n    })\r\n\r\n    uppy.on('upload-error', (file, error) => {\r\n      console.error('Upload error:', file?.name, error)\r\n      setError(`Upload failed: ${error.message}`)\r\n      onUploadError?.(error.message)\r\n    })\r\n\r\n    uppy.on('complete', (result) => {\r\n      console.log('Upload complete:', result)\r\n      setIsUploading(false)\r\n      setUploadProgress(0)\r\n      setUploadSpeed(0)\r\n      setTimeRemaining(0)\r\n    })\r\n\r\n    uppy.on('cancel-all', () => {\r\n      // Only log if we actually have files or are uploading\r\n      if (uppy.getFiles().length > 0 || isUploading) {\r\n        console.log('Upload cancelled - this may be due to user action or an error')\r\n      }\r\n      setIsUploading(false)\r\n      setUploadProgress(0)\r\n      setUploadSpeed(0)\r\n      setTimeRemaining(0)\r\n    })\r\n\r\n    uppyRef.current = uppy\r\n\r\n    return () => {\r\n      uppy.destroy()\r\n    }\r\n  }, [projectId, onUploadComplete, onUploadError])\r\n\r\n  // Control handlers\r\n  const handlePauseResume = () => {\r\n    if (!uppyRef.current) return\r\n\r\n    if (isPaused) {\r\n      uppyRef.current.resumeAll()\r\n      setIsPaused(false)\r\n    } else {\r\n      uppyRef.current.pauseAll()\r\n      setIsPaused(true)\r\n    }\r\n  }\r\n\r\n  const handleCancel = () => {\r\n    if (!uppyRef.current) return\r\n    uppyRef.current.cancelAll()\r\n    setSelectedFile(null)\r\n  }\r\n\r\n  const handleRemoveFile = () => {\r\n    if (!uppyRef.current || !selectedFile) return\r\n    uppyRef.current.removeFile(selectedFile.id)\r\n    setSelectedFile(null)\r\n  }\r\n\r\n  const handleStartUpload = () => {\r\n    if (!uppyRef.current) return\r\n    uppyRef.current.upload()\r\n  }\r\n\r\n  // Drag and drop handlers\r\n  const handleDragOver = useCallback((e: React.DragEvent) => {\r\n    e.preventDefault()\r\n    e.stopPropagation()\r\n    setIsDragOver(true)\r\n  }, [])\r\n\r\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\r\n    e.preventDefault()\r\n    e.stopPropagation()\r\n    setIsDragOver(false)\r\n  }, [])\r\n\r\n  const handleDrop = useCallback((e: React.DragEvent) => {\r\n    e.preventDefault()\r\n    e.stopPropagation()\r\n    setIsDragOver(false)\r\n\r\n    const files = Array.from(e.dataTransfer.files)\r\n    if (files.length > 0 && uppyRef.current) {\r\n      try {\r\n        const file = files[0]\r\n\r\n        // Clear any existing files first\r\n        uppyRef.current.getFiles().forEach(existingFile => {\r\n          uppyRef.current?.removeFile(existingFile.id)\r\n        })\r\n\r\n        uppyRef.current.addFile({\r\n          source: 'drag-drop',\r\n          name: file.name,\r\n          type: file.type,\r\n          data: file,\r\n          meta: {\r\n            projectId,\r\n          }\r\n        })\r\n      } catch (error) {\r\n        console.error('Error adding file:', error)\r\n        setError('Failed to add file. Please check the file type and size.')\r\n      }\r\n    }\r\n  }, [projectId])\r\n\r\n  const handleClick = useCallback(() => {\r\n    // Prevent multiple clicks while uploading or if file already selected\r\n    if (isUploading || selectedFile) {\r\n      return\r\n    }\r\n    console.log('Upload area clicked, opening file dialog')\r\n    hiddenFileInputRef.current?.click()\r\n  }, [isUploading, selectedFile])\r\n\r\n  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const files = e.target.files\r\n    console.log('File input changed:', files)\r\n\r\n    if (files && files.length > 0 && uppyRef.current) {\r\n      try {\r\n        const file = files[0]\r\n        console.log('Adding file to Uppy:', file.name, file.type, file.size)\r\n\r\n        // Clear any existing files first\r\n        uppyRef.current.getFiles().forEach(existingFile => {\r\n          uppyRef.current?.removeFile(existingFile.id)\r\n        })\r\n\r\n        uppyRef.current.addFile({\r\n          source: 'file-input',\r\n          name: file.name,\r\n          type: file.type,\r\n          data: file,\r\n          meta: {\r\n            projectId,\r\n          }\r\n        })\r\n      } catch (error) {\r\n        console.error('Error adding file to Uppy:', error)\r\n        setError('Failed to add file. Please check the file type and size.')\r\n      }\r\n    }\r\n    // Reset the input so the same file can be selected again\r\n    e.target.value = ''\r\n  }, [projectId])\r\n\r\n  const formatBytes = (bytes: number): string => {\r\n    if (bytes === 0) return '0 Bytes'\r\n    const k = 1024\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\r\n  }\r\n\r\n  const formatTime = (seconds: number): string => {\r\n    if (!isFinite(seconds) || seconds < 0) return '--'\r\n    \r\n    const hours = Math.floor(seconds / 3600)\r\n    const minutes = Math.floor((seconds % 3600) / 60)\r\n    const secs = Math.floor(seconds % 60)\r\n\r\n    if (hours > 0) {\r\n      return `${hours}h ${minutes}m ${secs}s`\r\n    } else if (minutes > 0) {\r\n      return `${minutes}m ${secs}s`\r\n    } else {\r\n      return `${secs}s`\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className={className}>\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center space-x-2\">\r\n            <Upload className=\"h-5 w-5\" />\r\n            <span>Upload Game Build</span>\r\n          </CardTitle>\r\n          <CardDescription>\r\n            Upload your game build ZIP files with robust, pausable uploads supporting files up to 24GB\r\n          </CardDescription>\r\n        </CardHeader>\r\n        <CardContent className=\"space-y-6\">\r\n          {/* File Drop Zone */}\r\n          <div\r\n            className={cn(\r\n              \"relative border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer\",\r\n              isDragOver\r\n                ? \"border-primary bg-primary/5\"\r\n                : \"border-muted-foreground/25 hover:border-muted-foreground/50\",\r\n              selectedFile && \"border-green-500 bg-green-50\"\r\n            )}\r\n            onDragOver={handleDragOver}\r\n            onDragLeave={handleDragLeave}\r\n            onDrop={handleDrop}\r\n            onClick={handleClick}\r\n          >\r\n            {/* Hidden file input */}\r\n            <input\r\n              ref={hiddenFileInputRef}\r\n              type=\"file\"\r\n              accept=\".zip,application/zip,application/x-zip-compressed\"\r\n              onChange={handleFileInputChange}\r\n              className=\"hidden\"\r\n            />\r\n            {!selectedFile ? (\r\n              <div className=\"space-y-4\">\r\n                <div className=\"mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center\">\r\n                  <Upload className=\"h-6 w-6 text-muted-foreground\" />\r\n                </div>\r\n                <div className=\"space-y-2\">\r\n                  <p className=\"text-lg font-medium\">\r\n                    {isDragOver ? \"Drop your file here\" : \"Drag & drop your build file\"}\r\n                  </p>\r\n                  <p className=\"text-sm text-muted-foreground\">\r\n                    or click to browse files\r\n                  </p>\r\n                  <p className=\"text-xs text-muted-foreground\">\r\n                    Supports ZIP files up to 24GB\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <div className=\"space-y-4\">\r\n                <div className=\"mx-auto w-12 h-12 rounded-full bg-green-100 flex items-center justify-center\">\r\n                  <File className=\"h-6 w-6 text-green-600\" />\r\n                </div>\r\n                <div className=\"space-y-2\">\r\n                  <p className=\"text-lg font-medium text-green-700\">File Selected</p>\r\n                  <div className=\"flex items-center justify-center space-x-2\">\r\n                    <FileText className=\"h-4 w-4 text-muted-foreground\" />\r\n                    <span className=\"font-medium\">{selectedFile.name}</span>\r\n                    <Badge variant=\"secondary\">{formatBytes(selectedFile.size || 0)}</Badge>\r\n                  </div>\r\n                  <div className=\"flex items-center justify-center space-x-2 pt-2\">\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={handleRemoveFile}\r\n                      className=\"flex items-center space-x-1\"\r\n                    >\r\n                      <X className=\"h-4 w-4\" />\r\n                      <span>Remove</span>\r\n                    </Button>\r\n                    {!isUploading && (\r\n                      <Button\r\n                        onClick={handleStartUpload}\r\n                        className=\"flex items-center space-x-1\"\r\n                      >\r\n                        <Upload className=\"h-4 w-4\" />\r\n                        <span>Start Upload</span>\r\n                      </Button>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Upload Progress */}\r\n          {isUploading && (\r\n            <Card>\r\n              <CardHeader className=\"pb-3\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <CardTitle className=\"text-base\">Uploading {selectedFile?.name}</CardTitle>\r\n                  <Badge variant={isPaused ? \"secondary\" : \"default\"}>\r\n                    {isPaused ? \"Paused\" : \"Uploading\"}\r\n                  </Badge>\r\n                </div>\r\n              </CardHeader>\r\n              <CardContent className=\"space-y-4\">\r\n                {/* Progress Bar */}\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"flex items-center justify-between text-sm\">\r\n                    <span className=\"font-medium\">{uploadProgress}% complete</span>\r\n                    <span className=\"text-muted-foreground\">\r\n                      {formatBytes(uploadSpeed)}/s\r\n                    </span>\r\n                  </div>\r\n                  <Progress value={uploadProgress} className=\"h-2\" />\r\n                </div>\r\n\r\n                {/* Upload Stats */}\r\n                <div className=\"grid grid-cols-2 gap-4\">\r\n                  <div className=\"flex items-center space-x-2 text-sm\">\r\n                    <Zap className=\"h-4 w-4 text-blue-500\" />\r\n                    <span className=\"text-muted-foreground\">Speed:</span>\r\n                    <span className=\"font-medium\">{formatBytes(uploadSpeed)}/s</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-2 text-sm\">\r\n                    <Clock className=\"h-4 w-4 text-orange-500\" />\r\n                    <span className=\"text-muted-foreground\">Time left:</span>\r\n                    <span className=\"font-medium\">{formatTime(timeRemaining)}</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <Separator />\r\n\r\n                {/* Upload Controls */}\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={handlePauseResume}\r\n                      className=\"flex items-center space-x-1\"\r\n                    >\r\n                      {isPaused ? <Play className=\"h-4 w-4\" /> : <Pause className=\"h-4 w-4\" />}\r\n                      <span>{isPaused ? 'Resume' : 'Pause'}</span>\r\n                    </Button>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={handleCancel}\r\n                      className=\"flex items-center space-x-1\"\r\n                    >\r\n                      <X className=\"h-4 w-4\" />\r\n                      <span>Cancel</span>\r\n                    </Button>\r\n                  </div>\r\n                  <p className=\"text-xs text-muted-foreground\">\r\n                    Upload continues in background when tab is minimized\r\n                  </p>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          )}\r\n\r\n          {/* Success Message */}\r\n          {success && (\r\n            <Alert className=\"border-green-200 bg-green-50\">\r\n              <CheckCircle className=\"h-4 w-4 text-green-600\" />\r\n              <AlertDescription className=\"text-green-700\">\r\n                {success}\r\n              </AlertDescription>\r\n            </Alert>\r\n          )}\r\n\r\n          {/* Error Message */}\r\n          {error && (\r\n            <Alert variant=\"destructive\">\r\n              <AlertCircle className=\"h-4 w-4\" />\r\n              <AlertDescription>\r\n                {error}\r\n              </AlertDescription>\r\n            </Alert>\r\n          )}\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AAzBA;;;;;;;;;;;;;;;AAuCO,SAAS,eAAe,EAC7B,SAAS,EACT,gBAAgB,EAChB,aAAa,EACb,SAAS,EACW;IACpB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC1C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IACpD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkD;IACjG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,2CAA2C;QAC3C,CAAA,GAAA,kIAAA,CAAA,yBAAsB,AAAD,IAAI,KAAK,CAAC,QAAQ,IAAI;QAE3C,kBAAkB;QAClB,MAAM,OAAO,IAAI,6IAAA,CAAA,UAAI,CAAO;YAC1B,IAAI;YACJ,aAAa;YACb,sBAAsB;YACtB,cAAc;gBACZ,aAAa,KAAK,OAAO,OAAO;gBAChC,kBAAkB;gBAClB,kBAAkB;oBAAC;oBAAQ;oBAAmB;iBAA+B;YAC/E;YACA,MAAM;gBACJ;YACF;QACF;QAEA,oCAAoC;QACpC,KAAK,GAAG,CAAC,mJAAA,CAAA,UAAK,EAAE;YACd,oBAAoB,CAAC,OAAS,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,MAAM,OAAO;YAC9D,OAAO;YACP,aAAa;gBAAC;gBAAG;gBAAM;gBAAM;aAAK;YAElC,0BAA0B;YAC1B,qBAAqB,OAAO;gBAC1B,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,kCAAkC;wBAC7D,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;4BACnB,UAAU,KAAK,IAAI;4BACnB,MAAM,KAAK,IAAI;4BACf;wBACF;oBACF;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,IAAI,MAAM;oBAClB;oBAEA,OAAO,MAAM,SAAS,IAAI;gBAC5B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,oCAAoC;oBAClD,MAAM;gBACR;YACF;YAEA,4CAA4C;YAC5C,uBAAuB,OAAO;gBAC5B,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,qCAAqC;wBAChE,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;4BACnB,UAAU,KAAK,IAAI;4BACnB,MAAM,KAAK,IAAI;4BACf;4BACA,UAAU,KAAK,IAAI;wBACrB;oBACF;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,IAAI,MAAM;oBAClB;oBAEA,OAAO,MAAM,SAAS,IAAI;gBAC5B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,oCAAoC;oBAClD,MAAM;gBACR;YACF;YAEA,6CAA6C;YAC7C,UAAU,OAAO,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,UAAU,EAAE;gBAClD,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,8BAA8B;wBACzD,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;4BACnB;4BACA;4BACA;wBACF;oBACF;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,IAAI,MAAM;oBAClB;oBAEA,OAAO,MAAM,SAAS,IAAI;gBAC5B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,uBAAuB;oBACrC,MAAM;gBACR;YACF;YAEA,4BAA4B;YAC5B,yBAAyB,OAAO,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,KAAK,EAAE;gBAC5D,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,uCAAuC;wBAClE,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;4BACnB;4BACA;4BACA;wBACF;oBACF;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,IAAI,MAAM;oBAClB;oBAEA,OAAO,MAAM,SAAS,IAAI;gBAC5B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,sCAAsC;oBACpD,MAAM;gBACR;YACF;YAEA,oCAAoC;YACpC,WAAW,OAAO,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE;gBACvC,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,+BAA+B;wBAC1D,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;4BACnB;4BACA;wBACF;oBACF;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,IAAI,MAAM;oBAClB;oBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,OAAO,KAAK,KAAK,IAAI,EAAE;gBACzB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,wBAAwB;oBACtC,OAAO,EAAE;gBACX;YACF;YAEA,yBAAyB;YACzB,sBAAsB,OAAO,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE;gBAClD,IAAI;oBACF,MAAM,MAAM,oCAAoC;wBAC9C,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;4BACnB;4BACA;wBACF;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,6CAA6C;gBAC/C;YACF;QACF;QAEA,2DAA2D;QAE3D,kBAAkB;QAClB,KAAK,EAAE,CAAC,cAAc,CAAC;YACrB,QAAQ,GAAG,CAAC,uBAAuB,KAAK,IAAI,EAAE;YAC9C,gBAAgB;YAChB,SAAS;YACT,WAAW;QACb;QAEA,KAAK,EAAE,CAAC,gBAAgB,CAAC;YACvB,QAAQ,GAAG,CAAC,2BAA2B,MAAM;YAC7C,gBAAgB;YAChB,SAAS;YACT,WAAW;QACb;QAEA,KAAK,EAAE,CAAC,sBAAsB,CAAC,MAAM;YACnC,QAAQ,KAAK,CAAC,4BAA4B,MAAM,MAAM;YACtD,SAAS,CAAC,yBAAyB,EAAE,MAAM,OAAO,EAAE;QACtD;QAEA,KAAK,EAAE,CAAC,SAAS,CAAC;YAChB,QAAQ,KAAK,CAAC,eAAe;YAC7B,SAAS,CAAC,cAAc,EAAE,MAAM,OAAO,EAAE;QAC3C;QAEA,KAAK,EAAE,CAAC,gBAAgB,CAAC,MAAM,OAAO;YACpC,QAAQ,KAAK,CAAC,0BAA0B,MAAM,MAAM,OAAO;YAC3D,SAAS,CAAC,eAAe,EAAE,MAAM,OAAO,EAAE;QAC5C;QAEA,KAAK,EAAE,CAAC,gBAAgB;YACtB,QAAQ,GAAG,CAAC;YACZ,eAAe;YACf,SAAS;YACT,WAAW;QACb;QAEA,KAAK,EAAE,CAAC,mBAAmB,CAAC,MAAM;YAChC,IAAI,QAAQ,UAAU;gBACpB,MAAM,aAAa,KAAK,KAAK,CAAC,AAAC,SAAS,aAAa,GAAG,CAAC,SAAS,UAAU,IAAI,CAAC,IAAK;gBACtF,kBAAkB;gBAElB,4CAA4C;gBAC5C,MAAM,UAAU,KAAK,GAAG,KAAK,SAAS,aAAa;gBACnD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,UAAU,IAAI,EAAE,mBAAmB;;gBAC3E,MAAM,YAAY,CAAC,CAAC,SAAS,UAAU,IAAI,CAAC,IAAI,SAAS,aAAa,IAAI,MAAM,UAAU;;gBAE1F,eAAe;gBACf,iBAAiB;YACnB;QACF;QAEA,KAAK,EAAE,CAAC,kBAAkB,OAAO,MAAM;YACrC,QAAQ,GAAG,CAAC,mBAAmB,MAAM;YACrC,QAAQ,GAAG,CAAC,yBAAyB,KAAK,SAAS,CAAC,UAAU,MAAM;YAEpE,IAAI;gBACF,kEAAkE;gBAClE,IAAI,QAAQ;gBAEZ,QAAQ,GAAG,CAAC,+BAA+B;gBAE3C,gGAAgG;gBAChG,IAAI,MAAM,MAAM,KAAK;oBACnB,0EAA0E;oBAC1E,QAAQ,KAAK,IAAI,CAAC,GAAG;oBACrB,QAAQ,GAAG,CAAC,gCAAgC;gBAC9C,OAAO,IAAI,SAAS,IAAI,EAAE,OAAO,QAAQ;oBACvC,4EAA4E;oBAC5E,QAAQ,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM;oBAClC,QAAQ,GAAG,CAAC,mCAAmC;gBACjD,OAAO,IAAI,SAAS,SAAS,IAAI,OAAO,SAAS,SAAS,KAAK,UAAU;oBACvE,IAAI;wBACF,2CAA2C;wBAC3C,MAAM,MAAM,IAAI,IAAI,SAAS,SAAS;wBACtC,IAAI,OAAO,IAAI,QAAQ,CAAC,SAAS,CAAC,GAAG,uBAAuB;;wBAC5D,kDAAkD;wBAClD,IAAI,KAAK,UAAU,CAAC,eAAe;4BACjC,OAAO,KAAK,SAAS,CAAC,aAAa,MAAM;wBAC3C;wBACA,QAAQ;oBACV,EAAE,OAAO,UAAU;wBACjB,QAAQ,IAAI,CAAC,6BAA6B,SAAS,SAAS,EAAE;wBAC9D,kEAAkE;wBAClE,IAAI,SAAS,SAAS,CAAC,QAAQ,CAAC,MAAM;4BACpC,MAAM,QAAQ,SAAS,SAAS,CAAC,KAAK,CAAC;4BACvC,QAAQ,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,EAAC,gCAAgC;wBAClE;oBACF;gBACF,OAAO,IAAI,SAAS,IAAI,EAAE,KAAK;oBAC7B,+EAA+E;oBAC/E,IAAI,OAAO,OAAO,SAAS,IAAI,CAAC,GAAG;oBACnC,IAAI,KAAK,UAAU,CAAC,eAAe;wBACjC,OAAO,KAAK,SAAS,CAAC,aAAa,MAAM;oBAC3C;oBACA,QAAQ;gBACV,OAAO,IAAI,SAAS,IAAI,EAAE,UAAU;oBAClC,8DAA8D;oBAC9D,MAAM,MAAM,IAAI,IAAI,SAAS,IAAI,CAAC,QAAQ;oBAC1C,IAAI,OAAO,IAAI,QAAQ,CAAC,SAAS,CAAC,GAAG,uBAAuB;;oBAC5D,kDAAkD;oBAClD,IAAI,KAAK,UAAU,CAAC,eAAe;wBACjC,OAAO,KAAK,SAAS,CAAC,aAAa,MAAM;oBAC3C;oBACA,QAAQ;gBACV;gBAEA,mFAAmF;gBACnF,IAAI,CAAC,SAAS,MAAM,MAAM;oBACxB,QAAQ,IAAI,CAAC,yEAAyE,KAAK,IAAI;oBAE/F,iFAAiF;oBACjF,6EAA6E;oBAC7E,QAAQ,KAAK,IAAI;oBACjB,QAAQ,GAAG,CAAC;gBACd;gBAEA,QAAQ,GAAG,CAAC,+BAA+B;gBAE3C,oCAAoC;gBACpC,MAAM,mBAAmB,MAAM,MAAM,6BAA6B;oBAChE,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,UAAU,MAAM;wBAChB;wBACA;wBACA,UAAU,MAAM;oBAClB;gBACF;gBAEA,IAAI,CAAC,iBAAiB,EAAE,EAAE;oBACxB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,SAAS,MAAM,iBAAiB,IAAI;gBAC1C,QAAQ,GAAG,CAAC,6BAA6B;gBACzC,WAAW,CAAC,+BAA+B,EAAE,MAAM,MAAM;gBACzD,mBAAmB;YACrB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,SAAS;gBACT,gBAAgB;YAClB;QACF;QAEA,KAAK,EAAE,CAAC,gBAAgB,CAAC,MAAM;YAC7B,QAAQ,KAAK,CAAC,iBAAiB,MAAM,MAAM;YAC3C,SAAS,CAAC,eAAe,EAAE,MAAM,OAAO,EAAE;YAC1C,gBAAgB,MAAM,OAAO;QAC/B;QAEA,KAAK,EAAE,CAAC,YAAY,CAAC;YACnB,QAAQ,GAAG,CAAC,oBAAoB;YAChC,eAAe;YACf,kBAAkB;YAClB,eAAe;YACf,iBAAiB;QACnB;QAEA,KAAK,EAAE,CAAC,cAAc;YACpB,sDAAsD;YACtD,IAAI,KAAK,QAAQ,GAAG,MAAM,GAAG,KAAK,aAAa;gBAC7C,QAAQ,GAAG,CAAC;YACd;YACA,eAAe;YACf,kBAAkB;YAClB,eAAe;YACf,iBAAiB;QACnB;QAEA,QAAQ,OAAO,GAAG;QAElB,OAAO;YACL,KAAK,OAAO;QACd;IACF,GAAG;QAAC;QAAW;QAAkB;KAAc;IAE/C,mBAAmB;IACnB,MAAM,oBAAoB;QACxB,IAAI,CAAC,QAAQ,OAAO,EAAE;QAEtB,IAAI,UAAU;YACZ,QAAQ,OAAO,CAAC,SAAS;YACzB,YAAY;QACd,OAAO;YACL,QAAQ,OAAO,CAAC,QAAQ;YACxB,YAAY;QACd;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ,OAAO,EAAE;QACtB,QAAQ,OAAO,CAAC,SAAS;QACzB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,cAAc;QACvC,QAAQ,OAAO,CAAC,UAAU,CAAC,aAAa,EAAE;QAC1C,gBAAgB;IAClB;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,QAAQ,OAAO,EAAE;QACtB,QAAQ,OAAO,CAAC,MAAM;IACxB;IAEA,yBAAyB;IACzB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK;QAC7C,IAAI,MAAM,MAAM,GAAG,KAAK,QAAQ,OAAO,EAAE;YACvC,IAAI;gBACF,MAAM,OAAO,KAAK,CAAC,EAAE;gBAErB,iCAAiC;gBACjC,QAAQ,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAA;oBACjC,QAAQ,OAAO,EAAE,WAAW,aAAa,EAAE;gBAC7C;gBAEA,QAAQ,OAAO,CAAC,OAAO,CAAC;oBACtB,QAAQ;oBACR,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,MAAM;oBACN,MAAM;wBACJ;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,SAAS;YACX;QACF;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,sEAAsE;QACtE,IAAI,eAAe,cAAc;YAC/B;QACF;QACA,QAAQ,GAAG,CAAC;QACZ,mBAAmB,OAAO,EAAE;IAC9B,GAAG;QAAC;QAAa;KAAa;IAE9B,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,QAAQ,GAAG,CAAC,uBAAuB;QAEnC,IAAI,SAAS,MAAM,MAAM,GAAG,KAAK,QAAQ,OAAO,EAAE;YAChD,IAAI;gBACF,MAAM,OAAO,KAAK,CAAC,EAAE;gBACrB,QAAQ,GAAG,CAAC,wBAAwB,KAAK,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,IAAI;gBAEnE,iCAAiC;gBACjC,QAAQ,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAA;oBACjC,QAAQ,OAAO,EAAE,WAAW,aAAa,EAAE;gBAC7C;gBAEA,QAAQ,OAAO,CAAC,OAAO,CAAC;oBACtB,QAAQ;oBACR,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,MAAM;oBACN,MAAM;wBACJ;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,SAAS;YACX;QACF;QACA,yDAAyD;QACzD,EAAE,MAAM,CAAC,KAAK,GAAG;IACnB,GAAG;QAAC;KAAU;IAEd,MAAM,cAAc,CAAC;QACnB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;YAAM;SAAK;QAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,SAAS,YAAY,UAAU,GAAG,OAAO;QAE9C,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;QAC9C,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAElC,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,EAAE,EAAE,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;QACzC,OAAO,IAAI,UAAU,GAAG;YACtB,OAAO,GAAG,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;QAC/B,OAAO;YACL,OAAO,GAAG,KAAK,CAAC,CAAC;QACnB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW;kBACd,cAAA,8OAAC,yHAAA,CAAA,OAAI;;8BACH,8OAAC,yHAAA,CAAA,aAAU;;sCACT,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC,yHAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAInB,8OAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,8OAAC;4BACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+FACA,aACI,gCACA,+DACJ,gBAAgB;4BAElB,YAAY;4BACZ,aAAa;4BACb,QAAQ;4BACR,SAAS;;8CAGT,8OAAC;oCACC,KAAK;oCACL,MAAK;oCACL,QAAO;oCACP,UAAU;oCACV,WAAU;;;;;;gCAEX,CAAC,6BACA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,aAAa,wBAAwB;;;;;;8DAExC,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;8DAG7C,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;yDAMjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAClD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEAAe,aAAa,IAAI;;;;;;sEAChD,8OAAC,0HAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAa,YAAY,aAAa,IAAI,IAAI;;;;;;;;;;;;8DAE/D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,2HAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;4DACT,WAAU;;8EAEV,8OAAC,4LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;;8EACb,8OAAC;8EAAK;;;;;;;;;;;;wDAEP,CAAC,6BACA,8OAAC,2HAAA,CAAA,SAAM;4DACL,SAAS;4DACT,WAAU;;8EAEV,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAUnB,6BACC,8OAAC,yHAAA,CAAA,OAAI;;8CACH,8OAAC,yHAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;;oDAAY;oDAAW,cAAc;;;;;;;0DAC1D,8OAAC,0HAAA,CAAA,QAAK;gDAAC,SAAS,WAAW,cAAc;0DACtC,WAAW,WAAW;;;;;;;;;;;;;;;;;8CAI7B,8OAAC,yHAAA,CAAA,cAAW;oCAAC,WAAU;;sDAErB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;;gEAAe;gEAAe;;;;;;;sEAC9C,8OAAC;4DAAK,WAAU;;gEACb,YAAY;gEAAa;;;;;;;;;;;;;8DAG9B,8OAAC,6HAAA,CAAA,WAAQ;oDAAC,OAAO;oDAAgB,WAAU;;;;;;;;;;;;sDAI7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;;gEAAe,YAAY;gEAAa;;;;;;;;;;;;;8DAE1D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAAe,WAAW;;;;;;;;;;;;;;;;;;sDAI9C,8OAAC,8HAAA,CAAA,YAAS;;;;;sDAGV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,2HAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;4DACT,WAAU;;gEAET,yBAAW,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;yFAAe,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EAC5D,8OAAC;8EAAM,WAAW,WAAW;;;;;;;;;;;;sEAE/B,8OAAC,2HAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;4DACT,WAAU;;8EAEV,8OAAC,4LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;;8EACb,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;8DAGV,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;wBASpD,yBACC,8OAAC,0HAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,0HAAA,CAAA,mBAAgB;oCAAC,WAAU;8CACzB;;;;;;;;;;;;wBAMN,uBACC,8OAAC,0HAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,0HAAA,CAAA,mBAAgB;8CACd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB", "debugId": null}}, {"offset": {"line": 2041, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2065, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2091, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,8OAAC,kKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,qMAAA,CAAA,aAAgB,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,qMAAA,CAAA,aAAgB,CAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,qMAAA,CAAA,aAAgB,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,qMAAA,CAAA,aAAgB,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2315, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,qMAAA,CAAA,aAAgB,CAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2366, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/enhanced-config-editor.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Switch } from '@/components/ui/switch'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Save, RotateCcw, Settings, MessageSquare, Lock, Loader2 } from 'lucide-react'\n\ninterface StreamPlayerConfig {\n  // Stream Settings\n  autoConnect?: boolean\n  touchInput?: boolean\n  keyBoardInput?: boolean\n  resolutionMode?: string\n  maxStreamQuality?: string\n  primaryCodec?: string\n  fallBackCodec?: string\n  \n  // Security Settings\n  isPasswordProtected?: boolean\n  password?: string\n  \n  // UI Messages\n  loadingMessage?: string\n  connectingMessage?: string\n  disconnectedMessage?: string\n  reconnectingMessage?: string\n  errorMessage?: string\n  connectButtonText?: string\n}\n\ninterface EnhancedConfigEditorProps {\n  projectId: string\n  currentConfig: StreamPlayerConfig\n  onConfigUpdate?: (newConfig: StreamPlayerConfig) => void\n  isAdmin?: boolean\n  className?: string\n}\n\nexport function EnhancedConfigEditor({\n  projectId,\n  currentConfig,\n  onConfigUpdate,\n  isAdmin = false,\n  className = ''\n}: EnhancedConfigEditorProps) {\n  const [isSaving, setIsSaving] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const [config, setConfig] = useState<StreamPlayerConfig>({\n    // Default values\n    autoConnect: false,\n    touchInput: true,\n    keyBoardInput: true,\n    resolutionMode: 'Dynamic Resolution Mode',\n    maxStreamQuality: '1080p (1920x1080)',\n    primaryCodec: 'H264',\n    fallBackCodec: 'VP8',\n    isPasswordProtected: false,\n    password: '',\n    loadingMessage: 'Loading stream...',\n    connectingMessage: 'Connecting to stream...',\n    disconnectedMessage: 'Stream disconnected',\n    reconnectingMessage: 'Reconnecting...',\n    errorMessage: 'Stream error occurred',\n    connectButtonText: 'Connect to Stream',\n    ...currentConfig\n  })\n\n  useEffect(() => {\n    setConfig({\n      autoConnect: false,\n      touchInput: true,\n      keyBoardInput: true,\n      resolutionMode: 'Dynamic Resolution Mode',\n      maxStreamQuality: '1080p (1920x1080)',\n      primaryCodec: 'H264',\n      fallBackCodec: 'VP8',\n      isPasswordProtected: false,\n      password: '',\n      loadingMessage: 'Loading stream...',\n      connectingMessage: 'Connecting to stream...',\n      disconnectedMessage: 'Stream disconnected',\n      reconnectingMessage: 'Reconnecting...',\n      errorMessage: 'Stream error occurred',\n      connectButtonText: 'Connect to Stream',\n      ...currentConfig\n    })\n  }, [currentConfig])\n\n  const handleSave = async () => {\n    try {\n      setIsSaving(true)\n      setError(null)\n\n      // Determine which API endpoint to use\n      const endpoint = isAdmin ? '/api/admin/projects' : `/api/projects/${projectId}`\n      \n      const body = isAdmin \n        ? { project_id: projectId, config }\n        : { config }\n\n      const response = await fetch(endpoint, {\n        method: 'PATCH',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(body),\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new Error(errorData.error || 'Failed to save configuration')\n      }\n\n      onConfigUpdate?.(config)\n      alert('Configuration saved successfully!')\n\n    } catch (err: any) {\n      setError(err.message)\n      console.error('Error saving config:', err)\n    } finally {\n      setIsSaving(false)\n    }\n  }\n\n  const handleReset = () => {\n    setConfig({\n      autoConnect: false,\n      touchInput: true,\n      keyBoardInput: true,\n      resolutionMode: 'Dynamic Resolution Mode',\n      maxStreamQuality: '1080p (1920x1080)',\n      primaryCodec: 'H264',\n      fallBackCodec: 'VP8',\n      isPasswordProtected: false,\n      password: '',\n      loadingMessage: 'Loading stream...',\n      connectingMessage: 'Connecting to stream...',\n      disconnectedMessage: 'Stream disconnected',\n      reconnectingMessage: 'Reconnecting...',\n      errorMessage: 'Stream error occurred',\n      connectButtonText: 'Connect to Stream',\n      ...currentConfig\n    })\n    setError(null)\n  }\n\n  const updateConfig = (key: keyof StreamPlayerConfig, value: any) => {\n    setConfig(prev => ({ ...prev, [key]: value }))\n  }\n\n  return (\n    <Card className={className}>\n      <CardHeader>\n        <CardTitle className=\"flex items-center\">\n          <Settings className=\"h-5 w-5 mr-2\" />\n          Stream Configuration\n        </CardTitle>\n        <CardDescription>\n          Configure stream settings, security, and user interface messages\n        </CardDescription>\n      </CardHeader>\n      \n      <CardContent>\n        {error && (\n          <div className=\"mb-4 p-3 bg-red-50 border border-red-200 rounded-md\">\n            <p className=\"text-sm text-red-600\">{error}</p>\n          </div>\n        )}\n\n        <Tabs defaultValue=\"stream\" className=\"space-y-4\">\n          <TabsList className=\"grid w-full grid-cols-3\">\n            <TabsTrigger value=\"stream\">Stream Settings</TabsTrigger>\n            <TabsTrigger value=\"security\">Security</TabsTrigger>\n            <TabsTrigger value=\"messages\">Messages</TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"stream\" className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"autoConnect\">Auto Connect</Label>\n                <div className=\"flex items-center space-x-2\">\n                  <Switch\n                    id=\"autoConnect\"\n                    checked={config.autoConnect}\n                    onCheckedChange={(checked) => updateConfig('autoConnect', checked)}\n                  />\n                  <span className=\"text-sm text-gray-600\">\n                    {config.autoConnect ? 'Automatically connect on load' : 'Show connect button'}\n                  </span>\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"touchInput\">Touch Input</Label>\n                <div className=\"flex items-center space-x-2\">\n                  <Switch\n                    id=\"touchInput\"\n                    checked={config.touchInput}\n                    onCheckedChange={(checked) => updateConfig('touchInput', checked)}\n                  />\n                  <span className=\"text-sm text-gray-600\">Enable touch controls</span>\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"keyBoardInput\">Keyboard Input</Label>\n                <div className=\"flex items-center space-x-2\">\n                  <Switch\n                    id=\"keyBoardInput\"\n                    checked={config.keyBoardInput}\n                    onCheckedChange={(checked) => updateConfig('keyBoardInput', checked)}\n                  />\n                  <span className=\"text-sm text-gray-600\">Enable keyboard controls</span>\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"resolutionMode\">Resolution Mode</Label>\n                <Select\n                  value={config.resolutionMode}\n                  onValueChange={(value) => updateConfig('resolutionMode', value)}\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"Dynamic Resolution Mode\">Dynamic Resolution</SelectItem>\n                    <SelectItem value=\"Fixed Resolution Mode\">Fixed Resolution</SelectItem>\n                    <SelectItem value=\"Adaptive Resolution Mode\">Adaptive Resolution</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"maxStreamQuality\">Max Stream Quality</Label>\n                <Select\n                  value={config.maxStreamQuality}\n                  onValueChange={(value) => updateConfig('maxStreamQuality', value)}\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"4K (3840x2160)\">4K (3840x2160)</SelectItem>\n                    <SelectItem value=\"1440p (2560x1440)\">1440p (2560x1440)</SelectItem>\n                    <SelectItem value=\"1080p (1920x1080)\">1080p (1920x1080)</SelectItem>\n                    <SelectItem value=\"720p (1280x720)\">720p (1280x720)</SelectItem>\n                    <SelectItem value=\"480p (854x480)\">480p (854x480)</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"primaryCodec\">Primary Codec</Label>\n                <Select\n                  value={config.primaryCodec}\n                  onValueChange={(value) => updateConfig('primaryCodec', value)}\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"H264\">H264</SelectItem>\n                    <SelectItem value=\"H265\">H265</SelectItem>\n                    <SelectItem value=\"VP8\">VP8</SelectItem>\n                    <SelectItem value=\"VP9\">VP9</SelectItem>\n                    <SelectItem value=\"AV1\">AV1</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"fallBackCodec\">Fallback Codec</Label>\n                <Select\n                  value={config.fallBackCodec}\n                  onValueChange={(value) => updateConfig('fallBackCodec', value)}\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"H264\">H264</SelectItem>\n                    <SelectItem value=\"VP8\">VP8</SelectItem>\n                    <SelectItem value=\"VP9\">VP9</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"security\" className=\"space-y-4\">\n            <div className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"isPasswordProtected\">Password Protection</Label>\n                <div className=\"flex items-center space-x-2\">\n                  <Switch\n                    id=\"isPasswordProtected\"\n                    checked={config.isPasswordProtected}\n                    onCheckedChange={(checked) => updateConfig('isPasswordProtected', checked)}\n                  />\n                  <span className=\"text-sm text-gray-600\">\n                    {config.isPasswordProtected ? 'Stream is password protected' : 'Stream is public'}\n                  </span>\n                </div>\n              </div>\n\n              {config.isPasswordProtected && (\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"password\">Stream Password</Label>\n                  <Input\n                    id=\"password\"\n                    type=\"password\"\n                    value={config.password}\n                    onChange={(e) => updateConfig('password', e.target.value)}\n                    placeholder=\"Enter stream password\"\n                  />\n                  <p className=\"text-xs text-gray-500\">\n                    Users will need this password to access the stream\n                  </p>\n                </div>\n              )}\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"messages\" className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"loadingMessage\">Loading Message</Label>\n                <Input\n                  id=\"loadingMessage\"\n                  value={config.loadingMessage}\n                  onChange={(e) => updateConfig('loadingMessage', e.target.value)}\n                  placeholder=\"Loading stream...\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"connectingMessage\">Connecting Message</Label>\n                <Input\n                  id=\"connectingMessage\"\n                  value={config.connectingMessage}\n                  onChange={(e) => updateConfig('connectingMessage', e.target.value)}\n                  placeholder=\"Connecting to stream...\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"disconnectedMessage\">Disconnected Message</Label>\n                <Input\n                  id=\"disconnectedMessage\"\n                  value={config.disconnectedMessage}\n                  onChange={(e) => updateConfig('disconnectedMessage', e.target.value)}\n                  placeholder=\"Stream disconnected\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"reconnectingMessage\">Reconnecting Message</Label>\n                <Input\n                  id=\"reconnectingMessage\"\n                  value={config.reconnectingMessage}\n                  onChange={(e) => updateConfig('reconnectingMessage', e.target.value)}\n                  placeholder=\"Reconnecting...\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"errorMessage\">Error Message</Label>\n                <Input\n                  id=\"errorMessage\"\n                  value={config.errorMessage}\n                  onChange={(e) => updateConfig('errorMessage', e.target.value)}\n                  placeholder=\"Stream error occurred\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"connectButtonText\">Connect Button Text</Label>\n                <Input\n                  id=\"connectButtonText\"\n                  value={config.connectButtonText}\n                  onChange={(e) => updateConfig('connectButtonText', e.target.value)}\n                  placeholder=\"Connect to Stream\"\n                />\n              </div>\n            </div>\n          </TabsContent>\n        </Tabs>\n\n        <div className=\"flex items-center justify-between pt-6 border-t\">\n          <Button\n            variant=\"outline\"\n            onClick={handleReset}\n            disabled={isSaving}\n          >\n            <RotateCcw className=\"h-4 w-4 mr-2\" />\n            Reset\n          </Button>\n          \n          <Button\n            onClick={handleSave}\n            disabled={isSaving}\n          >\n            {isSaving ? (\n              <>\n                <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                Saving...\n              </>\n            ) : (\n              <>\n                <Save className=\"h-4 w-4 mr-2\" />\n                Save Configuration\n              </>\n            )}\n          </Button>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAXA;;;;;;;;;;;AA4CO,SAAS,qBAAqB,EACnC,SAAS,EACT,aAAa,EACb,cAAc,EACd,UAAU,KAAK,EACf,YAAY,EAAE,EACY;IAC1B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;QACvD,iBAAiB;QACjB,aAAa;QACb,YAAY;QACZ,eAAe;QACf,gBAAgB;QAChB,kBAAkB;QAClB,cAAc;QACd,eAAe;QACf,qBAAqB;QACrB,UAAU;QACV,gBAAgB;QAChB,mBAAmB;QACnB,qBAAqB;QACrB,qBAAqB;QACrB,cAAc;QACd,mBAAmB;QACnB,GAAG,aAAa;IAClB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,UAAU;YACR,aAAa;YACb,YAAY;YACZ,eAAe;YACf,gBAAgB;YAChB,kBAAkB;YAClB,cAAc;YACd,eAAe;YACf,qBAAqB;YACrB,UAAU;YACV,gBAAgB;YAChB,mBAAmB;YACnB,qBAAqB;YACrB,qBAAqB;YACrB,cAAc;YACd,mBAAmB;YACnB,GAAG,aAAa;QAClB;IACF,GAAG;QAAC;KAAc;IAElB,MAAM,aAAa;QACjB,IAAI;YACF,YAAY;YACZ,SAAS;YAET,sCAAsC;YACtC,MAAM,WAAW,UAAU,wBAAwB,CAAC,cAAc,EAAE,WAAW;YAE/E,MAAM,OAAO,UACT;gBAAE,YAAY;gBAAW;YAAO,IAChC;gBAAE;YAAO;YAEb,MAAM,WAAW,MAAM,MAAM,UAAU;gBACrC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,iBAAiB;YACjB,MAAM;QAER,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;YACpB,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,cAAc;QAClB,UAAU;YACR,aAAa;YACb,YAAY;YACZ,eAAe;YACf,gBAAgB;YAChB,kBAAkB;YAClB,cAAc;YACd,eAAe;YACf,qBAAqB;YACrB,UAAU;YACV,gBAAgB;YAChB,mBAAmB;YACnB,qBAAqB;YACrB,qBAAqB;YACrB,cAAc;YACd,mBAAmB;YACnB,GAAG,aAAa;QAClB;QACA,SAAS;IACX;IAEA,MAAM,eAAe,CAAC,KAA+B;QACnD,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE;YAAM,CAAC;IAC9C;IAEA,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,yHAAA,CAAA,aAAU;;kCACT,8OAAC,yHAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAGvC,8OAAC,yHAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAKnB,8OAAC,yHAAA,CAAA,cAAW;;oBACT,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;kCAIzC,8OAAC,yHAAA,CAAA,OAAI;wBAAC,cAAa;wBAAS,WAAU;;0CACpC,8OAAC,yHAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,8OAAC,yHAAA,CAAA,cAAW;wCAAC,OAAM;kDAAS;;;;;;kDAC5B,8OAAC,yHAAA,CAAA,cAAW;wCAAC,OAAM;kDAAW;;;;;;kDAC9B,8OAAC,yHAAA,CAAA,cAAW;wCAAC,OAAM;kDAAW;;;;;;;;;;;;0CAGhC,8OAAC,yHAAA,CAAA,cAAW;gCAAC,OAAM;gCAAS,WAAU;0CACpC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,2HAAA,CAAA,SAAM;4DACL,IAAG;4DACH,SAAS,OAAO,WAAW;4DAC3B,iBAAiB,CAAC,UAAY,aAAa,eAAe;;;;;;sEAE5D,8OAAC;4DAAK,WAAU;sEACb,OAAO,WAAW,GAAG,kCAAkC;;;;;;;;;;;;;;;;;;sDAK9D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAa;;;;;;8DAC5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,2HAAA,CAAA,SAAM;4DACL,IAAG;4DACH,SAAS,OAAO,UAAU;4DAC1B,iBAAiB,CAAC,UAAY,aAAa,cAAc;;;;;;sEAE3D,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAI5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,2HAAA,CAAA,SAAM;4DACL,IAAG;4DACH,SAAS,OAAO,aAAa;4DAC7B,iBAAiB,CAAC,UAAY,aAAa,iBAAiB;;;;;;sEAE9D,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAI5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAiB;;;;;;8DAChC,8OAAC,2HAAA,CAAA,SAAM;oDACL,OAAO,OAAO,cAAc;oDAC5B,eAAe,CAAC,QAAU,aAAa,kBAAkB;;sEAEzD,8OAAC,2HAAA,CAAA,gBAAa;sEACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;;;;;;;;;;sEAEd,8OAAC,2HAAA,CAAA,gBAAa;;8EACZ,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;8EAA0B;;;;;;8EAC5C,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;8EAAwB;;;;;;8EAC1C,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;8EAA2B;;;;;;;;;;;;;;;;;;;;;;;;sDAKnD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAmB;;;;;;8DAClC,8OAAC,2HAAA,CAAA,SAAM;oDACL,OAAO,OAAO,gBAAgB;oDAC9B,eAAe,CAAC,QAAU,aAAa,oBAAoB;;sEAE3D,8OAAC,2HAAA,CAAA,gBAAa;sEACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;;;;;;;;;;sEAEd,8OAAC,2HAAA,CAAA,gBAAa;;8EACZ,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;8EAAiB;;;;;;8EACnC,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;8EAAoB;;;;;;8EACtC,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;8EAAoB;;;;;;8EACtC,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;8EAAkB;;;;;;8EACpC,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;8EAAiB;;;;;;;;;;;;;;;;;;;;;;;;sDAKzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,8OAAC,2HAAA,CAAA,SAAM;oDACL,OAAO,OAAO,YAAY;oDAC1B,eAAe,CAAC,QAAU,aAAa,gBAAgB;;sEAEvD,8OAAC,2HAAA,CAAA,gBAAa;sEACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;;;;;;;;;;sEAEd,8OAAC,2HAAA,CAAA,gBAAa;;8EACZ,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;8EAAO;;;;;;8EACzB,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;8EAAO;;;;;;8EACzB,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;8EAAM;;;;;;8EACxB,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;8EAAM;;;;;;8EACxB,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;8EAAM;;;;;;;;;;;;;;;;;;;;;;;;sDAK9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAgB;;;;;;8DAC/B,8OAAC,2HAAA,CAAA,SAAM;oDACL,OAAO,OAAO,aAAa;oDAC3B,eAAe,CAAC,QAAU,aAAa,iBAAiB;;sEAExD,8OAAC,2HAAA,CAAA,gBAAa;sEACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;;;;;;;;;;sEAEd,8OAAC,2HAAA,CAAA,gBAAa;;8EACZ,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;8EAAO;;;;;;8EACzB,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;8EAAM;;;;;;8EACxB,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;8EAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOlC,8OAAC,yHAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;0CACtC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAsB;;;;;;8DACrC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,2HAAA,CAAA,SAAM;4DACL,IAAG;4DACH,SAAS,OAAO,mBAAmB;4DACnC,iBAAiB,CAAC,UAAY,aAAa,uBAAuB;;;;;;sEAEpE,8OAAC;4DAAK,WAAU;sEACb,OAAO,mBAAmB,GAAG,iCAAiC;;;;;;;;;;;;;;;;;;wCAKpE,OAAO,mBAAmB,kBACzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW;;;;;;8DAC1B,8OAAC,0HAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,OAAO,QAAQ;oDACtB,UAAU,CAAC,IAAM,aAAa,YAAY,EAAE,MAAM,CAAC,KAAK;oDACxD,aAAY;;;;;;8DAEd,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;0CAQ7C,8OAAC,yHAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;0CACtC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAiB;;;;;;8DAChC,8OAAC,0HAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,OAAO,cAAc;oDAC5B,UAAU,CAAC,IAAM,aAAa,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDAC9D,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAoB;;;;;;8DACnC,8OAAC,0HAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,OAAO,iBAAiB;oDAC/B,UAAU,CAAC,IAAM,aAAa,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDACjE,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAsB;;;;;;8DACrC,8OAAC,0HAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,OAAO,mBAAmB;oDACjC,UAAU,CAAC,IAAM,aAAa,uBAAuB,EAAE,MAAM,CAAC,KAAK;oDACnE,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAsB;;;;;;8DACrC,8OAAC,0HAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,OAAO,mBAAmB;oDACjC,UAAU,CAAC,IAAM,aAAa,uBAAuB,EAAE,MAAM,CAAC,KAAK;oDACnE,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,8OAAC,0HAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,OAAO,YAAY;oDAC1B,UAAU,CAAC,IAAM,aAAa,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAC5D,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAoB;;;;;;8DACnC,8OAAC,0HAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,OAAO,iBAAiB;oDAC/B,UAAU,CAAC,IAAM,aAAa,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDACjE,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIxC,8OAAC,2HAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU;0CAET,yBACC;;sDACE,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA8B;;iEAInD;;sDACE,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;AASjD", "debugId": null}}, {"offset": {"line": 3400, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3572, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/build-history.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'\nimport {\n  History,\n  Download,\n  RotateCcw,\n  CheckCircle,\n  Clock,\n  Archive,\n  AlertCircle,\n  Upload,\n  Loader2,\n  Calendar,\n  HardDrive,\n  Trash2,\n  Play,\n  Zap\n} from 'lucide-react'\nimport { Build } from '@/lib/supabase'\n\ninterface BuildHistoryProps {\n  projectId: string\n  builds: Build[]\n  onBuildRevert?: (buildId: string) => void\n  onBuildDelete?: (buildId: string) => void\n  onBuildActivate?: (buildId: string) => void\n  onRefresh?: () => void\n  isAdmin?: boolean\n  className?: string\n}\n\nexport function BuildHistory({\n  projectId,\n  builds,\n  onBuildRevert,\n  onBuildDelete,\n  onBuildActivate,\n  onRefresh,\n  isAdmin = false,\n  className = ''\n}: BuildHistoryProps) {\n  const [isReverting, setIsReverting] = useState<string | null>(null)\n  const [isDeleting, setIsDeleting] = useState<string | null>(null)\n  const [isActivating, setIsActivating] = useState<string | null>(null)\n  const [selectedBuild, setSelectedBuild] = useState<Build | null>(null)\n\n  const getStatusIcon = (status: Build['status']) => {\n    switch (status) {\n      case 'active':\n        return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n      case 'uploading':\n        return <Upload className=\"h-4 w-4 text-blue-500\" />\n      case 'processing':\n        return <Clock className=\"h-4 w-4 text-yellow-500\" />\n      case 'inactive':\n        return <Play className=\"h-4 w-4 text-gray-500\" />\n      case 'archived':\n        return <Archive className=\"h-4 w-4 text-gray-500\" />\n      case 'failed':\n        return <AlertCircle className=\"h-4 w-4 text-red-500\" />\n      default:\n        return <Clock className=\"h-4 w-4 text-gray-500\" />\n    }\n  }\n\n  const getStatusColor = (status: Build['status']) => {\n    switch (status) {\n      case 'active':\n        return 'bg-green-100 text-green-800'\n      case 'uploading':\n        return 'bg-blue-100 text-blue-800'\n      case 'processing':\n        return 'bg-yellow-100 text-yellow-800'\n      case 'inactive':\n        return 'bg-gray-100 text-gray-600'\n      case 'archived':\n        return 'bg-gray-100 text-gray-800'\n      case 'failed':\n        return 'bg-red-100 text-red-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const formatFileSize = (bytes?: number) => {\n    if (!bytes) return 'Unknown size'\n    const sizes = ['Bytes', 'KB', 'MB', 'GB']\n    const i = Math.floor(Math.log(bytes) / Math.log(1024))\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n    })\n  }\n\n  const handleRevert = async (build: Build) => {\n    if (!onBuildRevert || build.is_current) return\n\n    const confirmRevert = confirm(\n      `Are you sure you want to revert to version ${build.version} (${build.original_filename || build.filename})? This will make it the current active build.`\n    )\n\n    if (!confirmRevert) return\n\n    try {\n      setIsReverting(build.id)\n      await onBuildRevert(build.id)\n      onRefresh?.()\n    } catch (error) {\n      console.error('Error reverting build:', error)\n      alert('Failed to revert build. Please try again.')\n    } finally {\n      setIsReverting(null)\n    }\n  }\n\n  const handleDelete = async (build: Build) => {\n    if (!onBuildDelete) return\n\n    // Allow deletion of all builds - users should have full control\n    const confirmDelete = confirm(\n      `Are you sure you want to delete version ${build.version} (${build.original_filename || build.filename})? This will permanently remove the ZIP file from storage and cannot be undone.`\n    )\n\n    if (!confirmDelete) return\n\n    try {\n      setIsDeleting(build.id)\n      await onBuildDelete(build.id)\n      onRefresh?.()\n    } catch (error) {\n      console.error('Error deleting build:', error)\n      alert('Failed to delete build. Please try again.')\n    } finally {\n      setIsDeleting(null)\n    }\n  }\n\n  const handleActivate = async (build: Build) => {\n    if (!onBuildActivate || build.is_current) return\n\n    const confirmActivate = confirm(\n      `Are you sure you want to activate version ${build.version} (${build.original_filename || build.filename})? This will make it the current active build and upload it to StreamPixel.`\n    )\n\n    if (!confirmActivate) return\n\n    try {\n      setIsActivating(build.id)\n      await onBuildActivate(build.id)\n      onRefresh?.()\n    } catch (error) {\n      console.error('Error activating build:', error)\n      alert('Failed to activate build. Please try again.')\n    } finally {\n      setIsActivating(null)\n    }\n  }\n\n  const activeBuild = builds.find(build => build.is_current)\n  const archivedBuilds = builds.filter(build => !build.is_current).slice(0, 10) // Show max 10 archived builds\n\n  return (\n    <Card className={className}>\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            <History className=\"h-5 w-5\" />\n            <CardTitle>Builds</CardTitle>\n            <Badge variant=\"secondary\">{builds.length} total</Badge>\n          </div>\n          \n          {onRefresh && (\n            <Button size=\"sm\" variant=\"outline\" onClick={onRefresh}>\n              Refresh\n            </Button>\n          )}\n        </div>\n        \n        <CardDescription>\n          View and manage build versions. Each project can have up to 2 active builds.\n          {!isAdmin && \" You can revert to any previous version.\"}\n        </CardDescription>\n      </CardHeader>\n      \n      <CardContent className=\"space-y-6\">\n        {/* Current Build */}\n        {activeBuild && (\n          <div>\n            \n            <div className=\"border border-green-200 rounded-lg p-4 bg-green-50\">\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-3 mb-2\">\n                    <h5 className=\"font-medium text-gray-900\">{activeBuild.original_filename || activeBuild.filename}</h5>\n                    <Badge className={getStatusColor(activeBuild.status)}>\n                      Version {activeBuild.version}\n                    </Badge>\n                    <Badge variant=\"outline\" className=\"bg-green-100 text-green-800\">\n                      Current\n                    </Badge>\n                  </div>\n                  \n                  <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\n                    <div className=\"flex items-center\">\n                      <Calendar className=\"h-4 w-4 mr-1\" />\n                      {formatDate(activeBuild.created_at)}\n                    </div>\n                    {activeBuild.file_size && (\n                      <div className=\"flex items-center\">\n                        <HardDrive className=\"h-4 w-4 mr-1\" />\n                        {formatFileSize(activeBuild.file_size)}\n                      </div>\n                    )}\n                    <div className=\"flex items-center\">\n                      {getStatusIcon(activeBuild.status)}\n                      <span className=\"ml-1 capitalize\">{activeBuild.status}</span>\n                    </div>\n                  </div>\n                  \n                  {activeBuild.streampixel_status && (\n                    <div className=\"mt-2\">\n                      <Badge\n                        variant=\"outline\"\n                        className={`text-xs ${\n                          activeBuild.streampixel_status === 'uploaded' ? 'bg-green-50 text-green-700 border-green-200' :\n                          activeBuild.streampixel_status === 'validation_failed' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :\n                          activeBuild.streampixel_status === 'failed' ? 'bg-red-50 text-red-700 border-red-200' :\n                          ''\n                        }`}\n                      >\n                       \n                      </Badge>\n                    </div>\n                  )}\n                </div>\n                \n                <div className=\"flex space-x-2\">\n                  <Dialog>\n                    <DialogTrigger asChild>\n                      <Button size=\"sm\" variant=\"outline\" onClick={() => setSelectedBuild(activeBuild)}>\n                        View Details\n                      </Button>\n                    </DialogTrigger>\n                    <DialogContent>\n                      <DialogHeader>\n                        <DialogTitle>Build Details - Version {activeBuild.version}</DialogTitle>\n                        <DialogDescription>\n                          Detailed information about this build\n                        </DialogDescription>\n                      </DialogHeader>\n                      <div className=\"space-y-4\">\n                        <div>\n                          <label className=\"text-sm font-medium text-gray-700\">Filename</label>\n                          <p className=\"text-sm text-gray-900\">{activeBuild.original_filename || activeBuild.filename}</p>\n                        </div>\n                        <div>\n                          <label className=\"text-sm font-medium text-gray-700\">Status</label>\n                          <div className=\"flex items-center mt-1\">\n                            {getStatusIcon(activeBuild.status)}\n                            <span className=\"ml-2 text-sm capitalize\">{activeBuild.status}</span>\n                          </div>\n                        </div>\n                        <div>\n                          <label className=\"text-sm font-medium text-gray-700\">Upload Date</label>\n                          <p className=\"text-sm text-gray-900\">{formatDate(activeBuild.created_at)}</p>\n                        </div>\n                        {activeBuild.file_size && (\n                          <div>\n                            <label className=\"text-sm font-medium text-gray-700\">File Size</label>\n                            <p className=\"text-sm text-gray-900\">{formatFileSize(activeBuild.file_size)}</p>\n                          </div>\n                        )}\n                        {activeBuild.streampixel_build_id && (\n                          <div>\n                            <label className=\"text-sm font-medium text-gray-700\">StreamPixel Build ID</label>\n                            <p className=\"text-sm text-gray-900 font-mono\">{activeBuild.streampixel_build_id}</p>\n                          </div>\n                        )}\n                        {activeBuild.error_message && (\n                          <div>\n                            <label className=\"text-sm font-medium text-red-700\">Error Message</label>\n                            <p className=\"text-sm text-red-900 bg-red-50 p-2 rounded\">{activeBuild.error_message}</p>\n                          </div>\n                        )}\n                      </div>\n                    </DialogContent>\n                  </Dialog>\n\n                  <Button size=\"sm\" variant=\"outline\">\n                    <Download className=\"h-4 w-4\" />\n                  </Button>\n\n                  {onBuildDelete && (\n                    <Button\n                      size=\"sm\"\n                      variant=\"destructive\"\n                      onClick={() => handleDelete(activeBuild)}\n                      disabled={isDeleting === activeBuild.id}\n                      title=\"Delete build\"\n                    >\n                      {isDeleting === activeBuild.id ? (\n                        <Loader2 className=\"h-4 w-4 animate-spin\" />\n                      ) : (\n                        <Trash2 className=\"h-4 w-4\" />\n                      )}\n                    </Button>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n          <div>\n           \n            <div className=\"space-y-3\">\n              {archivedBuilds.map((build, index) => (\n                <div key={build.id || `build-${index}-${build.original_filename || build.filename}-${build.version}`} className=\"border rounded-lg p-4 hover:bg-gray-50\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center space-x-3 mb-2\">\n                        <h5 className=\"font-medium text-gray-900\">{build.original_filename || build.filename}</h5>\n                        <Badge variant=\"outline\">Version {build.version}</Badge>\n                        <Badge className={getStatusColor(build.status)}>\n                          {build.status}\n                        </Badge>\n                        {build.streampixel_status && (\n                          <Badge\n                            variant=\"outline\"\n                            className={`text-xs ${\n                              build.streampixel_status === 'uploaded' ? 'bg-green-50 text-green-700 border-green-200' :\n                              build.streampixel_status === 'validation_failed' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :\n                              build.streampixel_status === 'failed' ? 'bg-red-50 text-red-700 border-red-200' :\n                              ''\n                            }`}\n                          >\n                            \n                          </Badge>\n                        )}\n                      </div>\n                      \n                      <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\n                        <div className=\"flex items-center\">\n                          <Calendar className=\"h-4 w-4 mr-1\" />\n                          {formatDate(build.created_at)}\n                        </div>\n                        {build.file_size && (\n                          <div className=\"flex items-center\">\n                            <HardDrive className=\"h-4 w-4 mr-1\" />\n                            {formatFileSize(build.file_size)}\n                          </div>\n                        )}\n                      </div>\n                      \n                      {build.error_message && (\n                        <div className=\"mt-2\">\n                          <p className=\"text-sm text-red-600 bg-red-50 p-2 rounded\">\n                            {build.error_message}\n                          </p>\n                        </div>\n                      )}\n                    </div>\n                    \n                    <div className=\"flex space-x-2\">\n                      {(build.status === 'archived' || build.status === 'inactive') && onBuildActivate && (\n                        <Button\n                          size=\"sm\"\n                          variant=\"default\"\n                          onClick={() => handleActivate(build)}\n                          disabled={isActivating === build.id}\n                        >\n                          {isActivating === build.id ? (\n                            <Loader2 className=\"h-4 w-4 animate-spin\" />\n                          ) : (\n                            <Zap className=\"h-4 w-4\" />\n                          )}\n                          {isActivating === build.id ? 'Activating...' : 'Activate'}\n                        </Button>\n                      )}\n\n                      <Button size=\"sm\" variant=\"outline\">\n                        <Download className=\"h-4 w-4\" />\n                      </Button>\n\n                      {onBuildDelete && (\n                        <Button\n                          size=\"sm\"\n                          variant=\"destructive\"\n                          onClick={() => handleDelete(build)}\n                          disabled={isDeleting === build.id}\n                          title=\"Delete build\"\n                        >\n                          {isDeleting === build.id ? (\n                            <Loader2 className=\"h-4 w-4 animate-spin\" />\n                          ) : (\n                            <Trash2 className=\"h-4 w-4\" />\n                          )}\n                        </Button>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n        {/* Empty State */}\n        {builds.length === 0 && (\n          <div className=\"text-center py-8\">\n            <History className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No builds yet</h3>\n            <p className=\"text-gray-600\">Upload your first build to get started.</p>\n          </div>\n        )}\n\n        {/* Build Limit Info */}\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <div className=\"flex items-start\">\n            <div className=\"flex-shrink-0\">\n              <AlertCircle className=\"h-5 w-5 text-blue-500\" />\n            </div>\n            <div className=\"ml-3\">\n              <h4 className=\"text-sm font-medium text-blue-900\">Build Management</h4>\n              <div className=\"mt-1 text-sm text-blue-700\">\n                <p>• Each project can have <strong>maximum 2 builds</strong> (active, inactive, or archived)</p>\n                <p>• New builds are <strong>inactive by default</strong> unless auto-release is enabled</p>\n                <p>• Only <strong>ZIP files</strong> are accepted for game builds</p>\n                <p>• You must <strong>delete a build</strong> to upload a new one when limit is reached</p>\n                <p>• Activate an inactive/archived build to make it current and upload to StreamPixel</p>\n                <p>• Activated builds show as <strong>processing</strong> until StreamPixel confirms they are live</p>\n                <p>• <strong>Any build can be deleted</strong> - you have full control over your builds</p>\n                <p>• Deleting a build permanently removes the ZIP file from storage</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAoCO,SAAS,aAAa,EAC3B,SAAS,EACT,MAAM,EACN,aAAa,EACb,aAAa,EACb,eAAe,EACf,SAAS,EACT,UAAU,KAAK,EACf,YAAY,EAAE,EACI;IAClB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjE,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,wMAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,OAAO,OAAO;QACnB,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,CAAC,EAAE;IAC3E;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,iBAAiB,MAAM,UAAU,EAAE;QAExC,MAAM,gBAAgB,QACpB,CAAC,2CAA2C,EAAE,MAAM,OAAO,CAAC,EAAE,EAAE,MAAM,iBAAiB,IAAI,MAAM,QAAQ,CAAC,8CAA8C,CAAC;QAG3J,IAAI,CAAC,eAAe;QAEpB,IAAI;YACF,eAAe,MAAM,EAAE;YACvB,MAAM,cAAc,MAAM,EAAE;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,eAAe;QAEpB,gEAAgE;QAChE,MAAM,gBAAgB,QACpB,CAAC,wCAAwC,EAAE,MAAM,OAAO,CAAC,EAAE,EAAE,MAAM,iBAAiB,IAAI,MAAM,QAAQ,CAAC,+EAA+E,CAAC;QAGzL,IAAI,CAAC,eAAe;QAEpB,IAAI;YACF,cAAc,MAAM,EAAE;YACtB,MAAM,cAAc,MAAM,EAAE;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI,CAAC,mBAAmB,MAAM,UAAU,EAAE;QAE1C,MAAM,kBAAkB,QACtB,CAAC,0CAA0C,EAAE,MAAM,OAAO,CAAC,EAAE,EAAE,MAAM,iBAAiB,IAAI,MAAM,QAAQ,CAAC,2EAA2E,CAAC;QAGvL,IAAI,CAAC,iBAAiB;QAEtB,IAAI;YACF,gBAAgB,MAAM,EAAE;YACxB,MAAM,gBAAgB,MAAM,EAAE;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,UAAU;IACzD,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,UAAU,EAAE,KAAK,CAAC,GAAG,IAAI,8BAA8B;;IAE5G,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,yHAAA,CAAA,aAAU;;kCACT,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC,yHAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAa,OAAO,MAAM;4CAAC;;;;;;;;;;;;;4BAG3C,2BACC,8OAAC,2HAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,SAAQ;gCAAU,SAAS;0CAAW;;;;;;;;;;;;kCAM5D,8OAAC,yHAAA,CAAA,kBAAe;;4BAAC;4BAEd,CAAC,WAAW;;;;;;;;;;;;;0BAIjB,8OAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;;oBAEpB,6BACC,8OAAC;kCAEC,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA6B,YAAY,iBAAiB,IAAI,YAAY,QAAQ;;;;;;kEAChG,8OAAC,0HAAA,CAAA,QAAK;wDAAC,WAAW,eAAe,YAAY,MAAM;;4DAAG;4DAC3C,YAAY,OAAO;;;;;;;kEAE9B,8OAAC,0HAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAA8B;;;;;;;;;;;;0DAKnE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DACnB,WAAW,YAAY,UAAU;;;;;;;oDAEnC,YAAY,SAAS,kBACpB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DACpB,eAAe,YAAY,SAAS;;;;;;;kEAGzC,8OAAC;wDAAI,WAAU;;4DACZ,cAAc,YAAY,MAAM;0EACjC,8OAAC;gEAAK,WAAU;0EAAmB,YAAY,MAAM;;;;;;;;;;;;;;;;;;4CAIxD,YAAY,kBAAkB,kBAC7B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;oDACJ,SAAQ;oDACR,WAAW,CAAC,QAAQ,EAClB,YAAY,kBAAkB,KAAK,aAAa,gDAChD,YAAY,kBAAkB,KAAK,sBAAsB,mDACzD,YAAY,kBAAkB,KAAK,WAAW,0CAC9C,IACA;;;;;;;;;;;;;;;;;kDAQV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2HAAA,CAAA,SAAM;;kEACL,8OAAC,2HAAA,CAAA,gBAAa;wDAAC,OAAO;kEACpB,cAAA,8OAAC,2HAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,SAAQ;4DAAU,SAAS,IAAM,iBAAiB;sEAAc;;;;;;;;;;;kEAIpF,8OAAC,2HAAA,CAAA,gBAAa;;0EACZ,8OAAC,2HAAA,CAAA,eAAY;;kFACX,8OAAC,2HAAA,CAAA,cAAW;;4EAAC;4EAAyB,YAAY,OAAO;;;;;;;kFACzD,8OAAC,2HAAA,CAAA,oBAAiB;kFAAC;;;;;;;;;;;;0EAIrB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAAoC;;;;;;0FACrD,8OAAC;gFAAE,WAAU;0FAAyB,YAAY,iBAAiB,IAAI,YAAY,QAAQ;;;;;;;;;;;;kFAE7F,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAAoC;;;;;;0FACrD,8OAAC;gFAAI,WAAU;;oFACZ,cAAc,YAAY,MAAM;kGACjC,8OAAC;wFAAK,WAAU;kGAA2B,YAAY,MAAM;;;;;;;;;;;;;;;;;;kFAGjE,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAAoC;;;;;;0FACrD,8OAAC;gFAAE,WAAU;0FAAyB,WAAW,YAAY,UAAU;;;;;;;;;;;;oEAExE,YAAY,SAAS,kBACpB,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAAoC;;;;;;0FACrD,8OAAC;gFAAE,WAAU;0FAAyB,eAAe,YAAY,SAAS;;;;;;;;;;;;oEAG7E,YAAY,oBAAoB,kBAC/B,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAAoC;;;;;;0FACrD,8OAAC;gFAAE,WAAU;0FAAmC,YAAY,oBAAoB;;;;;;;;;;;;oEAGnF,YAAY,aAAa,kBACxB,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAAmC;;;;;;0FACpD,8OAAC;gFAAE,WAAU;0FAA8C,YAAY,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAO9F,8OAAC,2HAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAQ;0DACxB,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;4CAGrB,+BACC,8OAAC,2HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,aAAa;gDAC5B,UAAU,eAAe,YAAY,EAAE;gDACvC,OAAM;0DAEL,eAAe,YAAY,EAAE,iBAC5B,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;yEAEnB,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUhC,8OAAC;kCAEC,cAAA,8OAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC;oCAAqG,WAAU;8CAC9G,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAA6B,MAAM,iBAAiB,IAAI,MAAM,QAAQ;;;;;;0EACpF,8OAAC,0HAAA,CAAA,QAAK;gEAAC,SAAQ;;oEAAU;oEAAS,MAAM,OAAO;;;;;;;0EAC/C,8OAAC,0HAAA,CAAA,QAAK;gEAAC,WAAW,eAAe,MAAM,MAAM;0EAC1C,MAAM,MAAM;;;;;;4DAEd,MAAM,kBAAkB,kBACvB,8OAAC,0HAAA,CAAA,QAAK;gEACJ,SAAQ;gEACR,WAAW,CAAC,QAAQ,EAClB,MAAM,kBAAkB,KAAK,aAAa,gDAC1C,MAAM,kBAAkB,KAAK,sBAAsB,mDACnD,MAAM,kBAAkB,KAAK,WAAW,0CACxC,IACA;;;;;;;;;;;;kEAOR,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEACnB,WAAW,MAAM,UAAU;;;;;;;4DAE7B,MAAM,SAAS,kBACd,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;oEACpB,eAAe,MAAM,SAAS;;;;;;;;;;;;;oDAKpC,MAAM,aAAa,kBAClB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;sEACV,MAAM,aAAa;;;;;;;;;;;;;;;;;0DAM5B,8OAAC;gDAAI,WAAU;;oDACZ,CAAC,MAAM,MAAM,KAAK,cAAc,MAAM,MAAM,KAAK,UAAU,KAAK,iCAC/D,8OAAC,2HAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,eAAe;wDAC9B,UAAU,iBAAiB,MAAM,EAAE;;4DAElC,iBAAiB,MAAM,EAAE,iBACxB,8OAAC,iNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;qFAEnB,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DAEhB,iBAAiB,MAAM,EAAE,GAAG,kBAAkB;;;;;;;kEAInD,8OAAC,2HAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,SAAQ;kEACxB,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;oDAGrB,+BACC,8OAAC,2HAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,aAAa;wDAC5B,UAAU,eAAe,MAAM,EAAE;wDACjC,OAAM;kEAEL,eAAe,MAAM,EAAE,iBACtB,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAEnB,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mCA9EpB,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM,iBAAiB,IAAI,MAAM,QAAQ,CAAC,CAAC,EAAE,MAAM,OAAO,EAAE;;;;;;;;;;;;;;;oBA0F3G,OAAO,MAAM,KAAK,mBACjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,wMAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAKjC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;wDAAE;sEAAwB,8OAAC;sEAAO;;;;;;wDAAyB;;;;;;;8DAC5D,8OAAC;;wDAAE;sEAAiB,8OAAC;sEAAO;;;;;;wDAA4B;;;;;;;8DACxD,8OAAC;;wDAAE;sEAAO,8OAAC;sEAAO;;;;;;wDAAkB;;;;;;;8DACpC,8OAAC;;wDAAE;sEAAW,8OAAC;sEAAO;;;;;;wDAAuB;;;;;;;8DAC7C,8OAAC;8DAAE;;;;;;8DACH,8OAAC;;wDAAE;sEAA2B,8OAAC;sEAAO;;;;;;wDAAmB;;;;;;;8DACzD,8OAAC;;wDAAE;sEAAE,8OAAC;sEAAO;;;;;;wDAAiC;;;;;;;8DAC9C,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB", "debugId": null}}, {"offset": {"line": 4679, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/stream-player.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { StreamPixelApplication } from 'streampixelsdk'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'\nimport {\n  Play,\n  Square,\n  Volume2,\n  VolumeX,\n  Maximize,\n  Loader2,\n  AlertCircle,\n  ExternalLink,\n  Code,\n  Lock,\n  Unlock\n} from 'lucide-react'\n\ninterface StreamPlayerProps {\n  projectId: string\n  buildId?: string\n  className?: string\n  config?: StreamPlayerConfig\n  showControls?: boolean\n  showHeader?: boolean\n  showEmbedButton?: boolean\n  width?: number\n  height?: number\n  isEmbedded?: boolean\n  enableIframeComms?: boolean\n}\n\ninterface StreamPlayerConfig {\n  autoConnect?: boolean\n  touchInput?: boolean\n  keyBoardInput?: boolean\n  resolutionMode?: string\n  maxStreamQuality?: string\n  primaryCodec?: string\n  fallBackCodec?: string\n  isPasswordProtected?: boolean\n  password?: string\n  loadingMessage?: string\n  connectingMessage?: string\n  disconnectedMessage?: string\n  reconnectingMessage?: string\n  errorMessage?: string\n  connectButtonText?: string\n}\n\nexport function StreamPlayer({\n  projectId,\n  buildId,\n  className = '',\n  config: propConfig,\n  showControls = true,\n  showHeader = true,\n  showEmbedButton = true,\n  width = 1280,\n  height = 720,\n  isEmbedded = false,\n  enableIframeComms = false\n}: StreamPlayerProps) {\n  const [isLoading, setIsLoading] = useState(false)\n  const [isPlaying, setIsPlaying] = useState(false)\n  const [isMuted, setIsMuted] = useState(false)\n  const [streamProjectId, setStreamProjectId] = useState<string | null>(null)\n  const [config, setConfig] = useState<StreamPlayerConfig | null>(null)\n  const [projectData, setProjectData] = useState<any>(null)\n  const [isFullscreen, setIsFullscreen] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const [streamStatus, setStreamStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected')\n  const [loadingMessage, setLoadingMessage] = useState<string>('Loading stream...')\n  const [isVideoReady, setIsVideoReady] = useState(false)\n  const [isPasswordPromptOpen, setIsPasswordPromptOpen] = useState(false)\n  const [enteredPassword, setEnteredPassword] = useState('')\n  const [isPasswordValid, setIsPasswordValid] = useState(false)\n  const [showEmbedDialog, setShowEmbedDialog] = useState(false)\n\n  const containerRef = useRef<HTMLDivElement>(null) // for overlays/background\n  const sdkMountRef = useRef<HTMLDivElement>(null)   // ONLY for SDK-injected DOM\n\n  const streamRef = useRef<any>(null)\n  const playerRef = useRef<any>(null)\n  const uiControlRef = useRef<any>(null)\n  const isInitializedRef = useRef<boolean>(false)\n  const isInitializingRef = useRef<boolean>(false)\n\n  // Project data and config loader\n  useEffect(() => {\n    const fetchProjectData = async () => {\n      try {\n        setIsLoading(false)\n        setError(null)\n        const response = await fetch(`/api/projects/${projectId}`)\n        if (!response.ok) throw new Error('Failed to fetch project data')\n        const responseData = await response.json()\n        const data = responseData.project || responseData\n        setProjectData(data)\n        setStreamProjectId(data.stream_project_id)\n        const finalConfig = propConfig || data.config || {}\n        setConfig(finalConfig)\n        if (!data.stream_project_id) throw new Error('No StreamPixel ID found in project data')\n      } catch (err) {\n        setError('Failed to load project configuration')\n      } finally {\n        setIsLoading(false)\n      }\n    }\n    if (projectId) fetchProjectData()\n  }, [projectId, propConfig])\n\n  // Clean up SDK DOM on unmount/disconnect\n  useEffect(() => {\n    return () => { cleanup() }\n  }, [])\n\n  // Auto-connect for embedded mode\n  useEffect(() => {\n    if (config?.autoConnect && streamProjectId && !isPlaying && !isInitializingRef.current) {\n      console.log('🚀 Auto-connecting stream...', {\n        autoConnect: config?.autoConnect,\n        streamProjectId,\n        isPlaying,\n        isInitializing: isInitializingRef.current\n      })\n      handlePlay()\n    }\n  }, [config?.autoConnect, streamProjectId, isPlaying])\n\n  // Prepare messages\n  const messages = {\n    loading: config?.loadingMessage ?? 'Loading stream...',\n    connecting: config?.connectingMessage ?? 'Connecting to stream...',\n    disconnected: config?.disconnectedMessage ?? 'Stream disconnected',\n    reconnecting: config?.reconnectingMessage ?? 'Reconnecting...',\n    error: config?.errorMessage ?? 'Stream error occurred',\n    connectButton: config?.connectButtonText ?? 'Connect to Stream'\n  }\n\n  const createStreamConfig = () => {\n    if (!streamProjectId || !config) return null\n    return {\n      AutoConnect: true,\n      appId: streamProjectId,\n      touchInput: config?.touchInput ?? true,\n      keyBoardInput: config?.keyBoardInput ?? true,\n      resolutionMode: (config?.resolutionMode as \"Dynamic Resolution Mode\" | \"Fixed Resolution Mode\" | \"Crop on Resize Mode\") ?? \"Dynamic Resolution Mode\",\n      maxStreamQuality: config?.maxStreamQuality ?? \"1080p (1920x1080)\",\n      primaryCodec: (config?.primaryCodec as \"H264\" | \"VP8\" | \"AV1\" | \"VP9\") ?? \"H264\",\n      fallBackCodec: (config?.fallBackCodec as \"H264\" | \"VP8\" | \"AV1\" | \"VP9\") ?? \"VP8\"\n    }\n  }\n\n const initializeStream = async () => {\n  if (isInitializingRef.current) return // guard against parallel inits\n  isInitializingRef.current = true\n  setIsLoading(true)\n  setError(null)\n\n\n\n  try {\n    const streamConfig = createStreamConfig()\n    if (!streamConfig) throw new Error('Cannot create stream config: missing required data')\n    const { appStream, pixelStreaming, UIControl } = await StreamPixelApplication(streamConfig)\n    streamRef.current = appStream\n    playerRef.current = pixelStreaming\n    uiControlRef.current = UIControl\n\n    // Inject SDK DOM\n    if (sdkMountRef.current && appStream.rootElement) {\n      if (appStream.rootElement.parentNode) {\n        appStream.rootElement.parentNode.removeChild(appStream.rootElement)\n      }\n      sdkMountRef.current.innerHTML = ''\n      sdkMountRef.current.appendChild(appStream.rootElement)\n    }\n\n    setupEventListeners()\n    isInitializedRef.current = true\n    setIsLoading(true)\n  } catch (err: any) {\n    setError(err.message || 'Failed to initialize stream')\n    setIsLoading(false)\n  } finally {\n    isInitializingRef.current = false\n  }\n}\n  const cleanup = () => {\n   try {\n  \n\n\n    if (streamRef.current?.stream?.disconnect) streamRef.current.stream.disconnect()\n    if (playerRef.current?.disconnect) playerRef.current.disconnect()\n\n  \n    if (sdkMountRef.current) sdkMountRef.current.innerHTML = ''\n  } catch (err) {}\n  // Nullify refs\n  streamRef.current = null\n  playerRef.current = null\n  uiControlRef.current = null\n  isInitializedRef.current = false\n  isInitializingRef.current = false\n  }\n\n  // Iframe communication system\n  const sendMessageToParent = (type: string, data: any = {}) => {\n    if (enableIframeComms && typeof window !== 'undefined' && window.parent !== window) {\n      try {\n        window.parent.postMessage({\n          type: `omnipixel-${type}`,\n          projectId,\n          buildId,\n          timestamp: new Date().toISOString(),\n          ...data\n        }, '*')\n      } catch (error) {\n        console.warn('Failed to send message to parent:', error)\n      }\n    }\n  }\n\n  // Listen for messages from parent iframe\n  useEffect(() => {\n    if (!enableIframeComms || typeof window === 'undefined') return\n\n    const handleMessage = (event: MessageEvent) => {\n      if (!event.data?.type?.startsWith('omnipixel-')) return\n\n      const { type, data } = event.data\n      console.log('📨 Received message from parent:', type, data)\n\n      switch (type) {\n        case 'omnipixel-connect':\n          if (!isPlaying) {\n            handlePlay()\n          }\n          break\n        case 'omnipixel-disconnect':\n          if (isPlaying) {\n            handlePause()\n          }\n          break\n        case 'omnipixel-mute':\n          setIsMuted(true)\n          break\n        case 'omnipixel-unmute':\n          setIsMuted(false)\n          break\n        case 'omnipixel-fullscreen':\n          setIsFullscreen(true)\n          break\n        case 'omnipixel-exit-fullscreen':\n          setIsFullscreen(false)\n          break\n        case 'omnipixel-send-input':\n          // Forward input to StreamPixel\n          if (playerRef.current && data.input) {\n            // Handle different input types\n            if (data.input.type === 'keyboard') {\n              // Send keyboard input to StreamPixel\n            } else if (data.input.type === 'mouse') {\n              // Send mouse input to StreamPixel\n            }\n          }\n          break\n      }\n    }\n\n    window.addEventListener('message', handleMessage)\n    return () => window.removeEventListener('message', handleMessage)\n  }, [enableIframeComms, isPlaying, projectId, buildId])\n\n  // Send status updates to parent\n  useEffect(() => {\n    if (enableIframeComms) {\n      sendMessageToParent('status-change', {\n        isLoading,\n        isPlaying,\n        streamStatus,\n        error,\n        isMuted,\n        isFullscreen\n      })\n    }\n  }, [enableIframeComms, isLoading, isPlaying, streamStatus, error, isMuted, isFullscreen])\n\n  // Event listeners (simplified for clarity, you can add more as needed)\n  const setupEventListeners = () => {\n    if (!streamRef.current) return\n\n    streamRef.current.onConnectAction = () => {\n      console.log('🔗 StreamPixel: Connect action triggered')\n      setStreamStatus('connecting')\n      setIsLoading(true)\n      setIsVideoReady(false)\n      setLoadingMessage(config?.connectingMessage ?? 'Connecting to stream...')\n      sendMessageToParent('connect-started')\n    }\n\n    streamRef.current.onWebRtcConnecting = () => {\n      console.log('🌐 StreamPixel: WebRTC connecting')\n      setStreamStatus('connecting')\n      setLoadingMessage(config?.connectingMessage ?? 'Establishing connection...')\n      sendMessageToParent('webrtc-connecting')\n    }\n\n    streamRef.current.onWebRtcConnected = () => {\n      console.log('✅ StreamPixel: WebRTC connected')\n      setStreamStatus('connecting')\n      setLoadingMessage('Initializing video stream...')\n      // Don't hide loading yet - wait for video to be ready\n      sendMessageToParent('webrtc-connected')\n    }\n\n    streamRef.current.onVideoInitialized = () => {\n      console.log('🎥 StreamPixel: Video initialized and ready')\n      console.log('🔄 Setting loading state to false - video ready')\n      setIsLoading(false) // Hide loading when video is actually ready\n      setIsVideoReady(true)\n      setStreamStatus('connected')\n      setIsPlaying(true)\n      sendMessageToParent('video-initialized')\n    }\n\n    streamRef.current.onDisconnect = () => {\n      console.log('🔌 StreamPixel: Disconnected')\n      setIsPlaying(false)\n      setIsLoading(false)\n      setIsVideoReady(false)\n      setStreamStatus('disconnected')\n      setLoadingMessage(config?.disconnectedMessage ?? 'Stream disconnected')\n      sendMessageToParent('disconnected')\n\n      // Use requestAnimationFrame for React-safe cleanup\n      requestAnimationFrame(() => {\n        cleanup()\n      })\n    }\n\n    // Additional StreamPixel events for iframe communication\n    if (streamRef.current.onDataChannelOpen) {\n      streamRef.current.onDataChannelOpen = () => {\n        console.log('📡 StreamPixel: Data channel opened')\n        sendMessageToParent('data-channel-open')\n      }\n    }\n\n    if (streamRef.current.onDataChannelMessage) {\n      streamRef.current.onDataChannelMessage = (message: any) => {\n        console.log('📨 StreamPixel: Data channel message', message)\n        sendMessageToParent('data-channel-message', { message })\n      }\n    }\n\n    // Unreal Engine specific events\n    if (streamRef.current.onUnrealMessage) {\n      streamRef.current.onUnrealMessage = (message: any) => {\n        console.log('🎮 Unreal Engine message:', message)\n        sendMessageToParent('unreal-message', { message })\n      }\n    }\n  }\n\n  // Control handlers\n  const handlePlay = async () => {\n    console.log('▶️ Play button clicked')\n    sendMessageToParent('play-requested')\n\n    console.log('🔄 Setting loading state to true')\n    setIsLoading(true)\n    setIsVideoReady(false)\n    setLoadingMessage(config?.loadingMessage ?? 'Initializing stream...')\n\n    cleanup()\n    await initializeStream()\n    setStreamStatus('connecting')\n    setIsPlaying(true)\n  }\n\n  const handlePause = () => {\n    console.log('⏸️ Pause button clicked')\n    sendMessageToParent('pause-requested')\n\n    playerRef.current?.disconnect()\n    streamRef.current?.stream?.disconnect()\n\n    // Use requestAnimationFrame for React-safe state updates\n    requestAnimationFrame(() => {\n      setIsPlaying(false)\n      setStreamStatus('disconnected')\n    })\n\n    cleanup()\n    window.location.reload();\n  }\n\n  const handleMute = () => {\n    console.log('🔇 Mute button clicked')\n    if (uiControlRef.current) {\n      uiControlRef.current.toggleAudio()\n      setIsMuted(!isMuted)\n      sendMessageToParent('mute-toggled', { isMuted: !isMuted })\n    }\n  }\n  const handleFullscreen = () => {\n    if (!containerRef.current) return\n    if (!isFullscreen) {\n      containerRef.current.requestFullscreen?.()\n    } else {\n      document.exitFullscreen?.()\n    }\n    setIsFullscreen(!isFullscreen)\n  }\n\n  // Password dialog handler\n  const handlePasswordSubmit = () => {\n    if (enteredPassword === config?.password) {\n      setIsPasswordValid(true)\n      setIsPasswordPromptOpen(false)\n    } else {\n      setError('Invalid password')\n    }\n  }\n\n  // Embed code utilities\n  const generateEmbedCode = () => {\n    const origin = typeof window !== 'undefined' ? window.location.origin : 'https://your-domain.com'\n    const params = new URLSearchParams()\n\n    // Add control visibility parameters\n    if (!showControls) params.set('hideControls', 'true')\n    if (!showHeader) params.set('hideHeader', 'true')\n    if (!showEmbedButton) params.set('hideEmbedButton', 'true')\n    if (config?.autoConnect) params.set('autoConnect', 'true')\n\n    const queryString = params.toString()\n    const embedUrl = `${origin}/embed/${projectId}${queryString ? `?${queryString}` : ''}`\n\n    return `<!-- OmniPixel Interactive Stream Embed -->\n<iframe\n  src=\"${embedUrl}\"\n  width=\"${width}\"\n  height=\"${height}\"\n  frameborder=\"0\"\n  allowfullscreen\n  allow=\"camera; microphone; fullscreen\"\n  style=\"border: none; border-radius: 8px;\">\n</iframe>\n\n<!-- Optional: Listen for iframe events -->\n<script>\nwindow.addEventListener('message', function(event) {\n  if (event.data?.type?.startsWith('omnipixel-')) {\n    console.log('Stream event:', event.data.type, event.data);\n\n    // Handle specific events\n    switch(event.data.type) {\n      case 'omnipixel-status-change':\n        console.log('Stream status:', event.data.streamStatus);\n        break;\n      case 'omnipixel-webrtc-connected':\n        console.log('Stream connected successfully');\n        break;\n      case 'omnipixel-unreal-message':\n        console.log('Unreal Engine message:', event.data.message);\n        break;\n    }\n  }\n});\n\n// Optional: Send commands to the stream\nfunction connectStream() {\n  document.querySelector('iframe').contentWindow.postMessage({\n    type: 'omnipixel-connect'\n  }, '*');\n}\n\nfunction disconnectStream() {\n  document.querySelector('iframe').contentWindow.postMessage({\n    type: 'omnipixel-disconnect'\n  }, '*');\n}\n</script>`\n  }\n  const copyEmbedCode = async () => {\n    try {\n      await navigator.clipboard.writeText(generateEmbedCode())\n      alert('Embed code copied to clipboard!')\n    } catch { }\n  }\n  const openInNewTab = () => {\n    if (typeof window !== 'undefined') {\n      const url = `${window.location.origin}/projects/${projectId}`\n      window.open(url, '_blank', 'noopener,noreferrer')\n    }\n  }\n\n  const getStatusColor = () => {\n    switch (streamStatus) {\n      case 'connected': return 'bg-green-100 text-green-800'\n      case 'connecting': return 'bg-yellow-100 text-yellow-800'\n      case 'disconnected': return 'bg-gray-100 text-gray-800'\n      case 'error': return 'bg-red-100 text-red-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n  const getStatusText = () => {\n    switch (streamStatus) {\n      case 'connected': return 'Connected'\n      case 'connecting': return 'Connecting...'\n      case 'disconnected': return 'Disconnected'\n      case 'error': return 'Error'\n      default: return 'Unknown'\n    }\n  }\n\n  // Password protection UI\n  if (config?.isPasswordProtected && !isPasswordValid && isPasswordPromptOpen) {\n    return (\n      <Card className={className}>\n        <CardContent className=\"pt-6\">\n          <div className=\"text-center space-y-4\">\n            <Lock className=\"h-12 w-12 mx-auto text-gray-400\" />\n            <h3 className=\"text-lg font-medium\">Password Protected</h3>\n            <p className=\"text-gray-600\">This stream requires a password to access.</p>\n            <div className=\"max-w-sm mx-auto space-y-3\">\n              <Input\n                type=\"password\"\n                placeholder=\"Enter password\"\n                value={enteredPassword}\n                onChange={(e) => setEnteredPassword(e.target.value)}\n                onKeyDown={(e) => e.key === 'Enter' && handlePasswordSubmit()}\n              />\n              <Button onClick={handlePasswordSubmit} className=\"w-full\">\n                <Unlock className=\"h-4 w-4 mr-2\" />\n                Access Stream\n              </Button>\n            </div>\n            {error && <p className=\"text-sm text-red-600\">{error}</p>}\n          </div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  // Full-screen embedded mode\n  if (isEmbedded && enableIframeComms) {\n    return (\n      <div className={`relative w-full h-full ${className}`}>\n        {/* Stream container */}\n        <div\n          ref={containerRef}\n          className=\"absolute inset-0 bg-black\"\n        >\n          {/* StreamPixel SDK mount point */}\n          <div\n            ref={sdkMountRef}\n            className=\"absolute inset-0 w-full h-full\"\n          />\n          {/* Loading overlay */}\n          {(isLoading || streamStatus === 'connecting') && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-900/95 z-20\">\n              <div className=\"text-center text-white\">\n                <Loader2 className=\"h-12 w-12 animate-spin mx-auto mb-4\" />\n                <p className=\"text-lg font-medium mb-2\">{loadingMessage}</p>\n                <p className=\"text-sm opacity-75\">Status: {getStatusText()}</p>\n              </div>\n            </div>\n          )}\n\n          {/* Error overlay */}\n          {error && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-red-900 z-20\">\n              <div className=\"text-center text-white p-4\">\n                <AlertCircle className=\"h-8 w-8 mx-auto mb-2\" />\n                <p>{error}</p>\n              </div>\n            </div>\n          )}\n\n          {/* Floating control buttons - no bar background */}\n          {showControls && (\n            <>\n              {/* Status badges - top left */}\n              <div className=\"absolute top-4 left-4 z-30 flex items-center space-x-2\">\n                <Badge className={getStatusColor()}>{getStatusText()}</Badge>\n                {buildId && (\n                  <Badge variant=\"outline\" className=\"text-white border-white/50 bg-black/70 backdrop-blur-sm\">\n                    Build: {buildId.slice(0, 8)}...\n                  </Badge>\n                )}\n              </div>\n\n              {/* Control buttons - bottom right */}\n              <div className=\"absolute bottom-4 right-4 z-30 flex items-center space-x-2\">\n                {!isPlaying ? (\n                  <Button\n                    onClick={handlePlay}\n                    size=\"sm\"\n                    className=\"bg-green-600/90 hover:bg-green-700 text-white font-medium px-4 backdrop-blur-sm shadow-lg\"\n                  >\n                    <Play className=\"h-4 w-4 mr-2\" />\n                    Connect\n                  </Button>\n                ) : (\n                  <Button\n                    onClick={handlePause}\n                    variant=\"destructive\"\n                    size=\"sm\"\n                    className=\"bg-red-600/90 hover:bg-red-700 text-white font-medium px-4 backdrop-blur-sm shadow-lg\"\n                  >\n                    <Square className=\"h-4 w-4 mr-2\" />\n                    Disconnect\n                  </Button>\n                )}\n\n                <Button\n                  onClick={handleMute}\n                  variant=\"outline\"\n                  size=\"sm\"\n                  className=\"text-white border-white/50 bg-black/70 hover:bg-black/80 hover:text-white backdrop-blur-sm shadow-lg\"\n                >\n                  {isMuted ? <VolumeX className=\"h-4 w-4\" /> : <Volume2 className=\"h-4 w-4\" />}\n                </Button>\n\n                <Button\n                  onClick={handleFullscreen}\n                  variant=\"outline\"\n                  size=\"sm\"\n                  className=\"text-white border-white/50 bg-black/70 hover:bg-black/80 hover:text-white backdrop-blur-sm shadow-lg\"\n                >\n                  <Maximize className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </>\n          )}\n        </div>\n      </div>\n    )\n  }\n\n  // Standard card mode\n  return (\n    <Card className={className}>\n      {showHeader && (\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <CardTitle>Interactive Stream</CardTitle>\n              <CardDescription>Real-time interactive streaming experience</CardDescription>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Badge className={getStatusColor()}>{getStatusText()}</Badge>\n              {buildId && (\n                <Badge variant=\"outline\">\n                  Build: {buildId.slice(0, 8)}...\n                </Badge>\n              )}\n              {showEmbedButton && (\n                <Dialog open={showEmbedDialog} onOpenChange={setShowEmbedDialog}>\n                  <DialogTrigger asChild>\n                    <Button variant=\"outline\" size=\"sm\">\n                      <Code className=\"h-4 w-4 mr-2\" />\n                      Embed\n                    </Button>\n                  </DialogTrigger>\n                  <DialogContent>\n                    <DialogHeader>\n                      <DialogTitle>Embed Stream</DialogTitle>\n                      <DialogDescription>Copy this code to embed the stream in your website</DialogDescription>\n                    </DialogHeader>\n                    <div className=\"space-y-4\">\n                      <div>\n                        <label className=\"text-sm font-medium\">Embed Code:</label>\n                        <textarea\n                          className=\"w-full mt-1 p-2 border rounded text-sm font-mono\"\n                          rows={6}\n                          readOnly\n                          value={generateEmbedCode()}\n                        />\n                      </div>\n                      <Button onClick={copyEmbedCode} className=\"w-full\">Copy Embed Code</Button>\n                    </div>\n                  </DialogContent>\n                </Dialog>\n              )}\n            </div>\n          </div>\n        </CardHeader>\n      )}\n\n      <CardContent>\n        {error && (\n          <div className=\"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg\">\n            <div className=\"flex items-center\">\n              <AlertCircle className=\"h-5 w-5 text-red-500 mr-2\" />\n              <p className=\"text-sm text-red-700\">{error}</p>\n            </div>\n          </div>\n        )}\n\n        <div\n          ref={containerRef}\n          className=\"relative bg-black rounded-lg overflow-hidden\"\n          style={{\n            width: '100%',\n            aspectRatio: `${width}/${height}`,\n            minHeight: '400px'\n          }}\n        >\n          {/* overlays */}\n          {(isLoading || streamStatus === 'connecting') && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-900/95 z-10\">\n              <div className=\"text-center text-white\">\n                <Loader2 className=\"h-12 w-12 animate-spin mx-auto mb-4\" />\n                <p className=\"text-lg font-medium mb-2\">{loadingMessage}</p>\n                <p className=\"text-sm opacity-75\">Status: {getStatusText()}</p>\n              </div>\n            </div>\n          )}\n          {!isLoading && !isPlaying && !error && streamStatus === 'disconnected' && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-900 z-10\">\n              <div className=\"text-center text-white space-y-4\">\n                <Play className=\"h-16 w-16 mx-auto opacity-50\" />\n                <div>\n                  <p className=\"text-lg mb-2\">Ready to Connect</p>\n                  <p className=\"text-sm opacity-75 mb-4\">{messages.disconnected}</p>\n                  {!config?.autoConnect && (\n                    <Button onClick={handlePlay} size=\"lg\">\n                      <Play className=\"h-5 w-5 mr-2\" />\n                      {messages.connectButton}\n                    </Button>\n                  )}\n                </div>\n              </div>\n            </div>\n          )}\n          {streamStatus === 'error' && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-900 z-10\">\n              <div className=\"text-center text-white\">\n                <AlertCircle className=\"h-16 w-16 mx-auto mb-4 text-red-500\" />\n                <p className=\"text-lg mb-2\">Connection Error</p>\n                <p className=\"text-sm opacity-75\">{error || messages.error}</p>\n              </div>\n            </div>\n          )}\n\n          {/* SDK DOM mounts ONLY here */}\n          <div\n            ref={sdkMountRef}\n            className=\"absolute inset-0 w-full h-full\"\n            style={{ zIndex: 0, pointerEvents: isLoading ? 'none' : 'auto' }}\n          />\n        </div>\n\n        {/* Controls */}\n        {showControls && (\n          <div className=\"mt-4 flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              {streamStatus === 'disconnected' || streamStatus === 'error' ? (\n                <Button onClick={handlePlay} disabled={isLoading}>\n                  <Play className=\"h-4 w-4\" />\n                  Connect\n                </Button>\n              ) : streamStatus === 'connecting' ? (\n                <Button disabled>\n                  <Loader2 className=\"h-4 w-4 animate-spin\" />\n                  {messages.connecting}\n                </Button>\n              ) : (\n                <Button variant=\"outline\" onClick={handlePause}>\n                  <Square className=\"h-4 w-4\" />\n                  Disconnect\n                </Button>\n              )}\n              <Button variant=\"outline\" onClick={handleMute}>\n                {isMuted ? (\n                  <VolumeX className=\"h-4 w-4\" />\n                ) : (\n                  <Volume2 className=\"h-4 w-4\" />\n                )}\n              </Button>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Button variant=\"outline\" size=\"sm\" onClick={handleFullscreen}>\n                <Maximize className=\"h-4 w-4\" />\n              </Button>\n              <Button variant=\"outline\" size=\"sm\" onClick={openInNewTab} title=\"Open in new tab\">\n                <ExternalLink className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        )}\n\n        <div className=\"mt-4 text-xs text-gray-500\">\n          <p>Stream ID: {streamProjectId}</p>\n          {buildId && <p>Build ID: {buildId}</p>}\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n\n// Extend window interface for StreamPixel SDK (optional if you want TS autocomplete)\ndeclare global {\n  interface Window {\n    StreamPixelApplication: any\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AAuDO,SAAS,aAAa,EAC3B,SAAS,EACT,OAAO,EACP,YAAY,EAAE,EACd,QAAQ,UAAU,EAClB,eAAe,IAAI,EACnB,aAAa,IAAI,EACjB,kBAAkB,IAAI,EACtB,QAAQ,IAAI,EACZ,SAAS,GAAG,EACZ,aAAa,KAAK,EAClB,oBAAoB,KAAK,EACP;IAClB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyD;IACxG,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB,MAAM,0BAA0B;;IAC5E,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB,MAAQ,4BAA4B;;IAE/E,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IACjC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAW;IACzC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAW;IAE1C,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,aAAa;gBACb,SAAS;gBACT,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW;gBACzD,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;gBAClC,MAAM,eAAe,MAAM,SAAS,IAAI;gBACxC,MAAM,OAAO,aAAa,OAAO,IAAI;gBACrC,eAAe;gBACf,mBAAmB,KAAK,iBAAiB;gBACzC,MAAM,cAAc,cAAc,KAAK,MAAM,IAAI,CAAC;gBAClD,UAAU;gBACV,IAAI,CAAC,KAAK,iBAAiB,EAAE,MAAM,IAAI,MAAM;YAC/C,EAAE,OAAO,KAAK;gBACZ,SAAS;YACX,SAAU;gBACR,aAAa;YACf;QACF;QACA,IAAI,WAAW;IACjB,GAAG;QAAC;QAAW;KAAW;IAE1B,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YAAQ;QAAU;IAC3B,GAAG,EAAE;IAEL,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,eAAe,mBAAmB,CAAC,aAAa,CAAC,kBAAkB,OAAO,EAAE;YACtF,QAAQ,GAAG,CAAC,gCAAgC;gBAC1C,aAAa,QAAQ;gBACrB;gBACA;gBACA,gBAAgB,kBAAkB,OAAO;YAC3C;YACA;QACF;IACF,GAAG;QAAC,QAAQ;QAAa;QAAiB;KAAU;IAEpD,mBAAmB;IACnB,MAAM,WAAW;QACf,SAAS,QAAQ,kBAAkB;QACnC,YAAY,QAAQ,qBAAqB;QACzC,cAAc,QAAQ,uBAAuB;QAC7C,cAAc,QAAQ,uBAAuB;QAC7C,OAAO,QAAQ,gBAAgB;QAC/B,eAAe,QAAQ,qBAAqB;IAC9C;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,OAAO;QACxC,OAAO;YACL,aAAa;YACb,OAAO;YACP,YAAY,QAAQ,cAAc;YAClC,eAAe,QAAQ,iBAAiB;YACxC,gBAAgB,AAAC,QAAQ,kBAAkG;YAC3H,kBAAkB,QAAQ,oBAAoB;YAC9C,cAAc,AAAC,QAAQ,gBAAmD;YAC1E,eAAe,AAAC,QAAQ,iBAAoD;QAC9E;IACF;IAED,MAAM,mBAAmB;QACxB,IAAI,kBAAkB,OAAO,EAAE,QAAO,+BAA+B;QACrE,kBAAkB,OAAO,GAAG;QAC5B,aAAa;QACb,SAAS;QAIT,IAAI;YACF,MAAM,eAAe;YACrB,IAAI,CAAC,cAAc,MAAM,IAAI,MAAM;YACnC,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG,MAAM,CAAA,GAAA,4JAAA,CAAA,yBAAsB,AAAD,EAAE;YAC9E,UAAU,OAAO,GAAG;YACpB,UAAU,OAAO,GAAG;YACpB,aAAa,OAAO,GAAG;YAEvB,iBAAiB;YACjB,IAAI,YAAY,OAAO,IAAI,UAAU,WAAW,EAAE;gBAChD,IAAI,UAAU,WAAW,CAAC,UAAU,EAAE;oBACpC,UAAU,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,UAAU,WAAW;gBACpE;gBACA,YAAY,OAAO,CAAC,SAAS,GAAG;gBAChC,YAAY,OAAO,CAAC,WAAW,CAAC,UAAU,WAAW;YACvD;YAEA;YACA,iBAAiB,OAAO,GAAG;YAC3B,aAAa;QACf,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;YACxB,aAAa;QACf,SAAU;YACR,kBAAkB,OAAO,GAAG;QAC9B;IACF;IACE,MAAM,UAAU;QACf,IAAI;YAIH,IAAI,UAAU,OAAO,EAAE,QAAQ,YAAY,UAAU,OAAO,CAAC,MAAM,CAAC,UAAU;YAC9E,IAAI,UAAU,OAAO,EAAE,YAAY,UAAU,OAAO,CAAC,UAAU;YAG/D,IAAI,YAAY,OAAO,EAAE,YAAY,OAAO,CAAC,SAAS,GAAG;QAC3D,EAAE,OAAO,KAAK,CAAC;QACf,eAAe;QACf,UAAU,OAAO,GAAG;QACpB,UAAU,OAAO,GAAG;QACpB,aAAa,OAAO,GAAG;QACvB,iBAAiB,OAAO,GAAG;QAC3B,kBAAkB,OAAO,GAAG;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,sBAAsB,CAAC,MAAc,OAAY,CAAC,CAAC;QACvD;;IAaF;IAEA,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAyD;;;QAEzD,MAAM;IA6CR,GAAG;QAAC;QAAmB;QAAW;QAAW;KAAQ;IAErD,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,mBAAmB;YACrB,oBAAoB,iBAAiB;gBACnC;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF;IACF,GAAG;QAAC;QAAmB;QAAW;QAAW;QAAc;QAAO;QAAS;KAAa;IAExF,uEAAuE;IACvE,MAAM,sBAAsB;QAC1B,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,UAAU,OAAO,CAAC,eAAe,GAAG;YAClC,QAAQ,GAAG,CAAC;YACZ,gBAAgB;YAChB,aAAa;YACb,gBAAgB;YAChB,kBAAkB,QAAQ,qBAAqB;YAC/C,oBAAoB;QACtB;QAEA,UAAU,OAAO,CAAC,kBAAkB,GAAG;YACrC,QAAQ,GAAG,CAAC;YACZ,gBAAgB;YAChB,kBAAkB,QAAQ,qBAAqB;YAC/C,oBAAoB;QACtB;QAEA,UAAU,OAAO,CAAC,iBAAiB,GAAG;YACpC,QAAQ,GAAG,CAAC;YACZ,gBAAgB;YAChB,kBAAkB;YAClB,sDAAsD;YACtD,oBAAoB;QACtB;QAEA,UAAU,OAAO,CAAC,kBAAkB,GAAG;YACrC,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC;YACZ,aAAa,QAAO,4CAA4C;YAChE,gBAAgB;YAChB,gBAAgB;YAChB,aAAa;YACb,oBAAoB;QACtB;QAEA,UAAU,OAAO,CAAC,YAAY,GAAG;YAC/B,QAAQ,GAAG,CAAC;YACZ,aAAa;YACb,aAAa;YACb,gBAAgB;YAChB,gBAAgB;YAChB,kBAAkB,QAAQ,uBAAuB;YACjD,oBAAoB;YAEpB,mDAAmD;YACnD,sBAAsB;gBACpB;YACF;QACF;QAEA,yDAAyD;QACzD,IAAI,UAAU,OAAO,CAAC,iBAAiB,EAAE;YACvC,UAAU,OAAO,CAAC,iBAAiB,GAAG;gBACpC,QAAQ,GAAG,CAAC;gBACZ,oBAAoB;YACtB;QACF;QAEA,IAAI,UAAU,OAAO,CAAC,oBAAoB,EAAE;YAC1C,UAAU,OAAO,CAAC,oBAAoB,GAAG,CAAC;gBACxC,QAAQ,GAAG,CAAC,wCAAwC;gBACpD,oBAAoB,wBAAwB;oBAAE;gBAAQ;YACxD;QACF;QAEA,gCAAgC;QAChC,IAAI,UAAU,OAAO,CAAC,eAAe,EAAE;YACrC,UAAU,OAAO,CAAC,eAAe,GAAG,CAAC;gBACnC,QAAQ,GAAG,CAAC,6BAA6B;gBACzC,oBAAoB,kBAAkB;oBAAE;gBAAQ;YAClD;QACF;IACF;IAEA,mBAAmB;IACnB,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC;QACZ,oBAAoB;QAEpB,QAAQ,GAAG,CAAC;QACZ,aAAa;QACb,gBAAgB;QAChB,kBAAkB,QAAQ,kBAAkB;QAE5C;QACA,MAAM;QACN,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,cAAc;QAClB,QAAQ,GAAG,CAAC;QACZ,oBAAoB;QAEpB,UAAU,OAAO,EAAE;QACnB,UAAU,OAAO,EAAE,QAAQ;QAE3B,yDAAyD;QACzD,sBAAsB;YACpB,aAAa;YACb,gBAAgB;QAClB;QAEA;QACA,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC;QACZ,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,WAAW;YAChC,WAAW,CAAC;YACZ,oBAAoB,gBAAgB;gBAAE,SAAS,CAAC;YAAQ;QAC1D;IACF;IACA,MAAM,mBAAmB;QACvB,IAAI,CAAC,aAAa,OAAO,EAAE;QAC3B,IAAI,CAAC,cAAc;YACjB,aAAa,OAAO,CAAC,iBAAiB;QACxC,OAAO;YACL,SAAS,cAAc;QACzB;QACA,gBAAgB,CAAC;IACnB;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB;QAC3B,IAAI,oBAAoB,QAAQ,UAAU;YACxC,mBAAmB;YACnB,wBAAwB;QAC1B,OAAO;YACL,SAAS;QACX;IACF;IAEA,uBAAuB;IACvB,MAAM,oBAAoB;QACxB,MAAM,SAAS,sCAAgC,0BAAyB;QACxE,MAAM,SAAS,IAAI;QAEnB,oCAAoC;QACpC,IAAI,CAAC,cAAc,OAAO,GAAG,CAAC,gBAAgB;QAC9C,IAAI,CAAC,YAAY,OAAO,GAAG,CAAC,cAAc;QAC1C,IAAI,CAAC,iBAAiB,OAAO,GAAG,CAAC,mBAAmB;QACpD,IAAI,QAAQ,aAAa,OAAO,GAAG,CAAC,eAAe;QAEnD,MAAM,cAAc,OAAO,QAAQ;QACnC,MAAM,WAAW,GAAG,OAAO,OAAO,EAAE,YAAY,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEtF,OAAO,CAAC;;OAEL,EAAE,SAAS;SACT,EAAE,MAAM;UACP,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAwCV,CAAC;IACR;IACA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,MAAM;QACR,EAAE,OAAM,CAAE;IACZ;IACA,MAAM,eAAe;QACnB;;IAIF;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IACA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,yBAAyB;IACzB,IAAI,QAAQ,uBAAuB,CAAC,mBAAmB,sBAAsB;QAC3E,qBACE,8OAAC,yHAAA,CAAA,OAAI;YAAC,WAAW;sBACf,cAAA,8OAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAG,WAAU;sCAAsB;;;;;;sCACpC,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAC7B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0HAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,WAAW,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;8CAEzC,8OAAC,2HAAA,CAAA,SAAM;oCAAC,SAAS;oCAAsB,WAAU;;sDAC/C,8OAAC,4MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;wBAItC,uBAAS,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;IAKzD;IAEA,4BAA4B;IAC5B,IAAI,cAAc,mBAAmB;QACnC,qBACE,8OAAC;YAAI,WAAW,CAAC,uBAAuB,EAAE,WAAW;sBAEnD,cAAA,8OAAC;gBACC,KAAK;gBACL,WAAU;;kCAGV,8OAAC;wBACC,KAAK;wBACL,WAAU;;;;;;oBAGX,CAAC,aAAa,iBAAiB,YAAY,mBAC1C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;oCAAE,WAAU;8CAA4B;;;;;;8CACzC,8OAAC;oCAAE,WAAU;;wCAAqB;wCAAS;;;;;;;;;;;;;;;;;;oBAMhD,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;8CAAG;;;;;;;;;;;;;;;;;oBAMT,8BACC;;0CAEE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0HAAA,CAAA,QAAK;wCAAC,WAAW;kDAAmB;;;;;;oCACpC,yBACC,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;;4CAA0D;4CACnF,QAAQ,KAAK,CAAC,GAAG;4CAAG;;;;;;;;;;;;;0CAMlC,8OAAC;gCAAI,WAAU;;oCACZ,CAAC,0BACA,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,MAAK;wCACL,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;6DAInC,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,SAAQ;wCACR,MAAK;wCACL,WAAU;;0DAEV,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAKvC,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAET,wBAAU,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAAe,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAGlE,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQpC;IAEA,qBAAqB;IACrB,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAW;;YACd,4BACC,8OAAC,yHAAA,CAAA,aAAU;0BACT,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC,yHAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,yHAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAEnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0HAAA,CAAA,QAAK;oCAAC,WAAW;8CAAmB;;;;;;gCACpC,yBACC,8OAAC,0HAAA,CAAA,QAAK;oCAAC,SAAQ;;wCAAU;wCACf,QAAQ,KAAK,CAAC,GAAG;wCAAG;;;;;;;gCAG/B,iCACC,8OAAC,2HAAA,CAAA,SAAM;oCAAC,MAAM;oCAAiB,cAAc;;sDAC3C,8OAAC,2HAAA,CAAA,gBAAa;4CAAC,OAAO;sDACpB,cAAA,8OAAC,2HAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;;kEAC7B,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;sDAIrC,8OAAC,2HAAA,CAAA,gBAAa;;8DACZ,8OAAC,2HAAA,CAAA,eAAY;;sEACX,8OAAC,2HAAA,CAAA,cAAW;sEAAC;;;;;;sEACb,8OAAC,2HAAA,CAAA,oBAAiB;sEAAC;;;;;;;;;;;;8DAErB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAsB;;;;;;8EACvC,8OAAC;oEACC,WAAU;oEACV,MAAM;oEACN,QAAQ;oEACR,OAAO;;;;;;;;;;;;sEAGX,8OAAC,2HAAA,CAAA,SAAM;4DAAC,SAAS;4DAAe,WAAU;sEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUnE,8OAAC,yHAAA,CAAA,cAAW;;oBACT,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAK3C,8OAAC;wBACC,KAAK;wBACL,WAAU;wBACV,OAAO;4BACL,OAAO;4BACP,aAAa,GAAG,MAAM,CAAC,EAAE,QAAQ;4BACjC,WAAW;wBACb;;4BAGC,CAAC,aAAa,iBAAiB,YAAY,mBAC1C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAE,WAAU;sDAA4B;;;;;;sDACzC,8OAAC;4CAAE,WAAU;;gDAAqB;gDAAS;;;;;;;;;;;;;;;;;;4BAIhD,CAAC,aAAa,CAAC,aAAa,CAAC,SAAS,iBAAiB,gCACtD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAe;;;;;;8DAC5B,8OAAC;oDAAE,WAAU;8DAA2B,SAAS,YAAY;;;;;;gDAC5D,CAAC,QAAQ,6BACR,8OAAC,2HAAA,CAAA,SAAM;oDAAC,SAAS;oDAAY,MAAK;;sEAChC,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf,SAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;;4BAOlC,iBAAiB,yBAChB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAE,WAAU;sDAAe;;;;;;sDAC5B,8OAAC;4CAAE,WAAU;sDAAsB,SAAS,SAAS,KAAK;;;;;;;;;;;;;;;;;0CAMhE,8OAAC;gCACC,KAAK;gCACL,WAAU;gCACV,OAAO;oCAAE,QAAQ;oCAAG,eAAe,YAAY,SAAS;gCAAO;;;;;;;;;;;;oBAKlE,8BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCACZ,iBAAiB,kBAAkB,iBAAiB,wBACnD,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS;wCAAY,UAAU;;0DACrC,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;+CAG5B,iBAAiB,6BACnB,8OAAC,2HAAA,CAAA,SAAM;wCAAC,QAAQ;;0DACd,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAClB,SAAS,UAAU;;;;;;6DAGtB,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;;0DACjC,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAIlC,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;kDAChC,wBACC,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;kDAC3C,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;wCAAc,OAAM;kDAC/D,cAAA,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMhC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAE;oCAAY;;;;;;;4BACd,yBAAW,8OAAC;;oCAAE;oCAAW;;;;;;;;;;;;;;;;;;;;;;;;;AAKpC", "debugId": null}}, {"offset": {"line": 5986, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/app/projects/%5Bid%5D/client.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { useRouter } from 'next/navigation'\r\nimport type { User } from '@supabase/supabase-js'\r\nimport type { Project, Build } from '@/lib/supabase'\r\nimport { Navigation } from '@/components/navigation'\r\nimport { UppyFileUpload } from '@/components/uppy-file-upload'\r\nimport { EnhancedConfigEditor } from '@/components/enhanced-config-editor'\r\nimport { BuildHistory } from '@/components/build-history'\r\nimport { StreamPlayer } from '@/components/stream-player'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Switch } from '@/components/ui/switch'\r\nimport { Label } from '@/components/ui/label'\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { ArrowLeft, Download, Calendar, Package, Loader2, Edit2, Save, X } from 'lucide-react'\r\nimport { Body, Meta, UploadResult } from '@uppy/core'\r\n\r\ntype ProjectWithBuilds = Project & { builds: Build[] }\r\n\r\ninterface Props {\r\n  user: User\r\n  project: ProjectWithBuilds\r\n  initialBuilds: Build[]\r\n}\r\n\r\nexport default function ProjectDetailClient({ user, project, initialBuilds }: Props) {\r\n  const router = useRouter()\r\n  const [builds, setBuilds] = useState<Build[]>(initialBuilds)\r\n  const [isEditing, setIsEditing] = useState(false)\r\n  const [editedName, setEditedName] = useState(project.name)\r\n  const [autoRelease, setAutoRelease] = useState(project.auto_release)\r\n  const [loading, setLoading] = useState(false)\r\n  const [error, setError] = useState<string | null>(null)\r\n\r\n  // Handler examples below\r\n\r\n  const fetchBuilds = async () => {\r\n    try {\r\n      setLoading(true)\r\n      setError(null)\r\n      const response = await fetch(`/api/projects/${project.id}/builds`)\r\n      if (!response.ok) throw new Error('Could not fetch builds')\r\n      const data = await response.json()\r\n      setBuilds(data.builds)\r\n    } catch (err:unknown) {\r\n      setError(err instanceof Error ? err.message : 'An unknown error occurred')\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n\r\n  const handleUploadComplete = (result: UploadResult<Meta, Body>) => {\r\n    if (result.build) {\r\n      setBuilds((prev) => {\r\n        const newBuild = result.build as Build;\r\n        return [newBuild, ...prev].sort((a, b) => b.version - a.version);\r\n      });\r\n    } else {\r\n      fetchBuilds()\r\n    }\r\n  }\r\n\r\n  const handleUploadError = (err: string) => setError(err)\r\n\r\n  const handleBuildRevert = async (buildId: string) => {\r\n    try {\r\n      setLoading(true)\r\n      const response = await fetch(`/api/projects/${project.id}/builds/revert`, {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ build_id: buildId }),\r\n      })\r\n      if (!response.ok) throw new Error('Failed to revert build')\r\n      await fetchBuilds()\r\n      alert('Build reverted successfully!')\r\n    } catch (err: unknown) {\r\n      setError(err instanceof Error ? err.message : 'An unknown error occurred')\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const handleBuildDelete = async (buildId: string) => {\r\n    try {\r\n      setLoading(true)\r\n      const response = await fetch(`/api/projects/${project.id}/builds/${buildId}`, { method: 'DELETE' })\r\n      if (!response.ok) throw new Error('Failed to delete build')\r\n      await fetchBuilds()\r\n      alert('Build deleted successfully!')\r\n    } catch (err: unknown) {\r\n      setError(err instanceof Error ? err.message : 'An unknown error occurred')\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const handleBuildActivate = async (buildId: string) => {\r\n    try {\r\n      setLoading(true)\r\n      setError(null)\r\n      console.log('Activating build:', buildId, 'for project:', project.id)\r\n\r\n      const response = await fetch(`/api/projects/${project.id}/builds/${buildId}`, {\r\n        method: 'PATCH',\r\n        headers: {\r\n          'Content-Type': 'application/json'\r\n        }\r\n      })\r\n\r\n      console.log('Activation response status:', response.status)\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json()\r\n        console.error('Activation failed:', errorData)\r\n        throw new Error(errorData.error || 'Failed to activate build')\r\n      }\r\n\r\n      const result = await response.json()\r\n      console.log('Activation result:', result)\r\n\r\n      await fetchBuilds()\r\n      alert('Build activated successfully!')\r\n    } catch (err: unknown) {\r\n      console.error('Build activation error:', err)\r\n      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred'\r\n      setError(errorMessage)\r\n      alert('Failed to activate build: ' + errorMessage)\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const handleSaveProjectName = async () => {\r\n    if (!editedName.trim()) return\r\n    try {\r\n      setLoading(true)\r\n      const response = await fetch(`/api/projects/${project.id}`, {\r\n        method: 'PATCH',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ name: editedName.trim() }),\r\n      })\r\n      if (!response.ok) throw new Error('Failed to update project name')\r\n      setIsEditing(false)\r\n    } catch (err: unknown) {\r\n      setError(err instanceof Error ? err.message : 'An unknown error occurred')\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const handleCancelEdit = () => {\r\n    setEditedName(project.name)\r\n    setIsEditing(false)\r\n  }\r\n\r\n  const handleAutoReleaseToggle = async (enabled: boolean) => {\r\n    try {\r\n      setLoading(true)\r\n      const response = await fetch(`/api/projects/${project.id}`, {\r\n        method: 'PATCH',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ auto_release: enabled }),\r\n      })\r\n      if (!response.ok) throw new Error('Failed to update auto-release setting')\r\n      setAutoRelease(enabled)\r\n    } catch (err: unknown) {\r\n      setError(err instanceof Error ? err.message : 'An unknown error occurred')\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit',\r\n    })\r\n  }\r\n\r\n  // Render (shortened: fill in with your actual layout/logic)\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <Navigation />\r\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        <Button variant=\"ghost\" onClick={() => router.push('/dashboard')} className=\"mb-4\">\r\n          <ArrowLeft className=\"h-4 w-4 mr-2\" /> Back to Dashboard\r\n        </Button>\r\n\r\n        <div className=\"flex items-center space-x-2 mb-2\">\r\n          {isEditing ? (\r\n            <>\r\n              <Input value={editedName} onChange={e => setEditedName(e.target.value)} />\r\n              <Button size=\"sm\" onClick={handleSaveProjectName}><Save className=\"h-4 w-4\" /></Button>\r\n              <Button size=\"sm\" variant=\"outline\" onClick={handleCancelEdit}><X className=\"h-4 w-4\" /></Button>\r\n            </>\r\n          ) : (\r\n            <>\r\n              <h1 className=\"text-3xl font-bold text-gray-900\">{project.name}</h1>\r\n              <Button size=\"sm\" variant=\"ghost\" onClick={() => setIsEditing(true)}>\r\n                <Edit2 className=\"h-4 w-4\" />\r\n              </Button>\r\n            </>\r\n          )}\r\n        </div>\r\n        <p className=\"text-gray-600\">Stream Project ID: <span className=\"font-mono\">{project.stream_project_id}</span></p>\r\n        <Badge variant=\"secondary\">{builds.length} build{builds.length !== 1 ? 's' : ''}</Badge>\r\n\r\n        {/* Main Content */}\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8\">\r\n          <div className=\"lg:col-span-2 space-y-6\">\r\n            <StreamPlayer\r\n              projectId={project.id}\r\n              buildId={builds.find(b => b.is_current)?.id}\r\n              config={project.config}\r\n              showControls={true}\r\n              showHeader={true}\r\n              showEmbedButton={true}\r\n            />\r\n            <EnhancedConfigEditor\r\n              projectId={project.id}\r\n              currentConfig={project.config || {}}\r\n              onConfigUpdate={(newConfig) => { /* update logic here */ }}\r\n              isAdmin={false}\r\n            />\r\n            {/* Conditionally show upload section based on build limits */}\r\n            {builds.length < 2 ? (\r\n              <UppyFileUpload\r\n                    projectId={project.id}\r\n                    onUploadComplete={handleUploadComplete}\r\n                    onUploadError={handleUploadError}\r\n                  />\r\n            ) : (\r\n              <Card>\r\n                <CardHeader>\r\n                  <CardTitle>Build Limit Reached</CardTitle>\r\n                  <CardDescription>\r\n                    You have reached the maximum of 2 builds per project. Delete an existing build to upload a new one.\r\n                  </CardDescription>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <p className=\"text-sm text-gray-600\">\r\n                    Active builds: {builds.filter(b => !['failed', 'archived'].includes(b.status)).length}/2\r\n                  </p>\r\n                </CardContent>\r\n              </Card>\r\n            )}\r\n            <BuildHistory\r\n              projectId={project.id}\r\n              builds={builds}\r\n              onBuildRevert={handleBuildRevert}\r\n              onBuildDelete={handleBuildDelete}\r\n              onBuildActivate={handleBuildActivate}\r\n              onRefresh={fetchBuilds}\r\n              isAdmin={false}\r\n            />\r\n          </div>\r\n          {/* Sidebar Info */}\r\n          <div>\r\n            <Card>\r\n              <CardHeader><CardTitle>Project Settings</CardTitle></CardHeader>\r\n              <CardContent className=\"space-y-4\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div>\r\n                    <Label htmlFor=\"auto-release\" className=\"text-sm font-medium text-gray-700\">\r\n                      Auto-Release\r\n                    </Label>\r\n                    <p className=\"text-xs text-gray-500\">\r\n                      Automatically activate new builds when uploaded\r\n                    </p>\r\n                  </div>\r\n                  <Switch\r\n                    id=\"auto-release\"\r\n                    checked={autoRelease}\r\n                    onCheckedChange={handleAutoReleaseToggle}\r\n                    disabled={loading}\r\n                  />\r\n                </div>\r\n                <div><p className=\"text-sm font-medium text-gray-500\">Created</p>\r\n                <p className=\"text-sm text-gray-900\">{formatDate(project.created_at)}</p></div>\r\n                <div><p className=\"text-sm font-medium text-gray-500\">Last Updated</p>\r\n                <p className=\"text-sm text-gray-900\">{formatDate(project.updated_at)}</p></div>\r\n                <div><p className=\"text-sm font-medium text-gray-500\">Total Builds</p>\r\n                <p className=\"text-sm text-gray-900\">{builds.length}</p></div>\r\n              </CardContent>\r\n            </Card>\r\n          </div>\r\n        </div>\r\n       \r\n        {error && (\r\n          <div className=\"mt-4 text-red-600 text-center\">{error}</div>\r\n        )}\r\n        {loading && (\r\n          <div className=\"fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50\">\r\n            <Loader2 className=\"h-10 w-10 animate-spin text-white\" />\r\n          </div>\r\n        )}\r\n      </main>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAjBA;;;;;;;;;;;;;;;;AA4Be,SAAS,oBAAoB,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa,EAAS;IACjF,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,IAAI;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,YAAY;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,yBAAyB;IAEzB,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,QAAQ,EAAE,CAAC,OAAO,CAAC;YACjE,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,UAAU,KAAK,MAAM;QACvB,EAAE,OAAO,KAAa;YACpB,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAGA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,OAAO,KAAK,EAAE;YAChB,UAAU,CAAC;gBACT,MAAM,WAAW,OAAO,KAAK;gBAC7B,OAAO;oBAAC;uBAAa;iBAAK,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,OAAO,GAAG,EAAE,OAAO;YACjE;QACF,OAAO;YACL;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC,MAAgB,SAAS;IAEpD,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,QAAQ,EAAE,CAAC,cAAc,CAAC,EAAE;gBACxE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,UAAU;gBAAQ;YAC3C;YACA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,MAAM;YACN,MAAM;QACR,EAAE,OAAO,KAAc;YACrB,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE;gBAAE,QAAQ;YAAS;YACjG,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,MAAM;YACN,MAAM;QACR,EAAE,OAAO,KAAc;YACrB,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,WAAW;YACX,SAAS;YACT,QAAQ,GAAG,CAAC,qBAAqB,SAAS,gBAAgB,QAAQ,EAAE;YAEpE,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE;gBAC5E,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC,+BAA+B,SAAS,MAAM;YAE1D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,QAAQ,GAAG,CAAC,sBAAsB;YAElC,MAAM;YACN,MAAM;QACR,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,MAAM,+BAA+B;QACvC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,WAAW,IAAI,IAAI;QACxB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,QAAQ,EAAE,EAAE,EAAE;gBAC1D,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,MAAM,WAAW,IAAI;gBAAG;YACjD;YACA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,aAAa;QACf,EAAE,OAAO,KAAc;YACrB,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,cAAc,QAAQ,IAAI;QAC1B,aAAa;IACf;IAEA,MAAM,0BAA0B,OAAO;QACrC,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,QAAQ,EAAE,EAAE,EAAE;gBAC1D,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,cAAc;gBAAQ;YAC/C;YACA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,eAAe;QACjB,EAAE,OAAO,KAAc;YACrB,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,4DAA4D;IAC5D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yHAAA,CAAA,aAAU;;;;;0BACX,8OAAC;gBAAK,WAAU;;kCACd,8OAAC,2HAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,SAAS,IAAM,OAAO,IAAI,CAAC;wBAAe,WAAU;;0CAC1E,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAGxC,8OAAC;wBAAI,WAAU;kCACZ,0BACC;;8CACE,8OAAC,0HAAA,CAAA,QAAK;oCAAC,OAAO;oCAAY,UAAU,CAAA,IAAK,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;8CACrE,8OAAC,2HAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAS;8CAAuB,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAClE,8OAAC,2HAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAQ;oCAAU,SAAS;8CAAkB,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;yDAG9E;;8CACE,8OAAC;oCAAG,WAAU;8CAAoC,QAAQ,IAAI;;;;;;8CAC9D,8OAAC,2HAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAQ;oCAAQ,SAAS,IAAM,aAAa;8CAC5D,cAAA,8OAAC,kMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;kCAKzB,8OAAC;wBAAE,WAAU;;4BAAgB;0CAAmB,8OAAC;gCAAK,WAAU;0CAAa,QAAQ,iBAAiB;;;;;;;;;;;;kCACtG,8OAAC,0HAAA,CAAA,QAAK;wBAAC,SAAQ;;4BAAa,OAAO,MAAM;4BAAC;4BAAO,OAAO,MAAM,KAAK,IAAI,MAAM;;;;;;;kCAG7E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,+HAAA,CAAA,eAAY;wCACX,WAAW,QAAQ,EAAE;wCACrB,SAAS,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,UAAU,GAAG;wCACzC,QAAQ,QAAQ,MAAM;wCACtB,cAAc;wCACd,YAAY;wCACZ,iBAAiB;;;;;;kDAEnB,8OAAC,2IAAA,CAAA,uBAAoB;wCACnB,WAAW,QAAQ,EAAE;wCACrB,eAAe,QAAQ,MAAM,IAAI,CAAC;wCAClC,gBAAgB,CAAC,aAAwC;wCACzD,SAAS;;;;;;oCAGV,OAAO,MAAM,GAAG,kBACf,8OAAC,qIAAA,CAAA,iBAAc;wCACT,WAAW,QAAQ,EAAE;wCACrB,kBAAkB;wCAClB,eAAe;;;;;6DAGrB,8OAAC,yHAAA,CAAA,OAAI;;0DACH,8OAAC,yHAAA,CAAA,aAAU;;kEACT,8OAAC,yHAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,yHAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,8OAAC,yHAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAE,WAAU;;wDAAwB;wDACnB,OAAO,MAAM,CAAC,CAAA,IAAK,CAAC;gEAAC;gEAAU;6DAAW,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM;wDAAC;;;;;;;;;;;;;;;;;;kDAK9F,8OAAC,+HAAA,CAAA,eAAY;wCACX,WAAW,QAAQ,EAAE;wCACrB,QAAQ;wCACR,eAAe;wCACf,eAAe;wCACf,iBAAiB;wCACjB,WAAW;wCACX,SAAS;;;;;;;;;;;;0CAIb,8OAAC;0CACC,cAAA,8OAAC,yHAAA,CAAA,OAAI;;sDACH,8OAAC,yHAAA,CAAA,aAAU;sDAAC,cAAA,8OAAC,yHAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDACvB,8OAAC,yHAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC,0HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAe,WAAU;8EAAoC;;;;;;8EAG5E,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAIvC,8OAAC,2HAAA,CAAA,SAAM;4DACL,IAAG;4DACH,SAAS;4DACT,iBAAiB;4DACjB,UAAU;;;;;;;;;;;;8DAGd,8OAAC;;sEAAI,8OAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACtD,8OAAC;4DAAE,WAAU;sEAAyB,WAAW,QAAQ,UAAU;;;;;;;;;;;;8DACnE,8OAAC;;sEAAI,8OAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACtD,8OAAC;4DAAE,WAAU;sEAAyB,WAAW,QAAQ,UAAU;;;;;;;;;;;;8DACnE,8OAAC;;sEAAI,8OAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACtD,8OAAC;4DAAE,WAAU;sEAAyB,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAM1D,uBACC,8OAAC;wBAAI,WAAU;kCAAiC;;;;;;oBAEjD,yBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAM/B", "debugId": null}}]}