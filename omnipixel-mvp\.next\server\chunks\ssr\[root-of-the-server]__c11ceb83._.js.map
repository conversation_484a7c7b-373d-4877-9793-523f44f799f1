{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/lib/auth-context'\nimport { Button } from '@/components/ui/button'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { User, Settings, LogOut, Shield } from 'lucide-react'\n\nexport function Navigation() {\n  const { user, profile, signOut } = useAuth()\n  const router = useRouter()\n\n  const handleSignOut = async () => {\n    await signOut()\n    router.push('/login')\n  }\n\n  if (!user) return null\n\n  return (\n    <nav className=\"border-b bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/dashboard\" className=\"flex-shrink-0\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Omnipixel</h1>\n            </Link>\n            <div className=\"hidden md:ml-6 md:flex md:space-x-8\">\n              <Link\n                href=\"/dashboard\"\n                className=\"text-gray-900 hover:text-gray-700 px-3 py-2 text-sm font-medium\"\n              >\n                Dashboard\n              </Link>\n              {profile?.role === 'platform_admin' && (\n                <Link\n                  href=\"/admin\"\n                  className=\"text-gray-900 hover:text-gray-700 px-3 py-2 text-sm font-medium flex items-center gap-1\"\n                >\n                  <Shield className=\"h-4 w-4\" />\n                  Admin\n                </Link>\n              )}\n            </div>\n          </div>\n          <div className=\"flex items-center\">\n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                  <User className=\"h-4 w-4\" />\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                <div className=\"flex items-center justify-start gap-2 p-2\">\n                  <div className=\"flex flex-col space-y-1 leading-none\">\n                    <p className=\"font-medium\">{user.email}</p>\n                    <p className=\"text-xs text-muted-foreground\">\n                      {profile?.role === 'platform_admin' ? 'Platform Admin' : 'User'}\n                    </p>\n                  </div>\n                </div>\n                <DropdownMenuSeparator />\n                <DropdownMenuItem>\n                  <Settings className=\"mr-2 h-4 w-4\" />\n                  <span>Settings</span>\n                </DropdownMenuItem>\n                {profile?.role === 'platform_admin' && (\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/admin\">\n                      <Shield className=\"mr-2 h-4 w-4\" />\n                      <span>Admin Panel</span>\n                    </Link>\n                  </DropdownMenuItem>\n                )}\n                <DropdownMenuSeparator />\n                <DropdownMenuItem onClick={handleSignOut}>\n                  <LogOut className=\"mr-2 h-4 w-4\" />\n                  <span>Log out</span>\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAbA;;;;;;;;AAeO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACzC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;0CAChC,cAAA,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;oCAGA,SAAS,SAAS,kCACjB,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;kCAMtC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,qIAAA,CAAA,eAAY;;8CACX,8OAAC,qIAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,WAAU;kDAChC,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAGpB,8OAAC,qIAAA,CAAA,sBAAmB;oCAAC,WAAU;oCAAO,OAAM;oCAAM,UAAU;;sDAC1D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAe,KAAK,KAAK;;;;;;kEACtC,8OAAC;wDAAE,WAAU;kEACV,SAAS,SAAS,mBAAmB,mBAAmB;;;;;;;;;;;;;;;;;sDAI/D,8OAAC,qIAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,qIAAA,CAAA,mBAAgB;;8DACf,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;8DAAK;;;;;;;;;;;;wCAEP,SAAS,SAAS,kCACjB,8OAAC,qIAAA,CAAA,mBAAgB;4CAAC,OAAO;sDACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;;kEACT,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;sDAIZ,8OAAC,qIAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,qIAAA,CAAA,mBAAgB;4CAAC,SAAS;;8DACzB,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB", "debugId": null}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 665, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 786, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1002, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/app/admin/client.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport { useRouter } from 'next/navigation'\r\nimport { Navigation } from '@/components/navigation'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Label } from '@/components/ui/label'\r\nimport {\r\n  Card, CardContent, CardDescription, CardHeader, CardTitle,\r\n} from '@/components/ui/card'\r\nimport {\r\n  Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger,\r\n} from '@/components/ui/dialog'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport {\r\n  Plus, Users, Package, Loader2, ArrowLeft, Calendar, User as UserIcon, Edit2, Trash2, Eye,\r\n} from 'lucide-react'\r\nimport { User } from '@/types/supabase'\r\nimport { StreamPixelConfig } from 'streampixelsdk'\r\nimport { Profile } from '@/lib/supabase'\r\n\r\ninterface AdminProject {\r\n  id: string\r\n  name: string\r\n  stream_project_id: string\r\n  user_id: string\r\n  config: StreamPixelConfig // You can import StreamPixelConfig if needed\r\n  created_at: string\r\n  updated_at: string\r\n  profiles: {\r\n    email: string\r\n    role: string\r\n  }\r\n  builds: Array<{\r\n    id: string\r\n    filename: string\r\n    original_filename?: string\r\n    version: number\r\n    created_at: string\r\n  }>\r\n}\r\n\r\nexport default function AdminClient({\r\n  user,\r\n  profile,\r\n  initialProjects,\r\n}: {\r\n  user: User\r\n  profile: Profile\r\n  initialProjects: AdminProject[]\r\n}) {\r\n  const router = useRouter()\r\n  const [projects, setProjects] = useState<AdminProject[]>(initialProjects)\r\n  const [loading, setLoading] = useState(false)\r\n  const [error, setError] = useState<string | null>(null)\r\n  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)\r\n  const [isCreating, setIsCreating] = useState(false)\r\n  const [newProject, setNewProject] = useState({\r\n    name: '',\r\n    stream_project_id: '',\r\n    user_email: '',\r\n  })\r\n\r\n  // Fetch projects from API for mutations\r\n  const fetchProjects = async () => {\r\n    try {\r\n      setLoading(true)\r\n      setError(null)\r\n      const response = await fetch('/api/admin/projects')\r\n      if (!response.ok) {\r\n        const errorData = await response.json()\r\n        throw new Error(errorData.error || 'Failed to fetch projects')\r\n      }\r\n      const data = await response.json()\r\n      setProjects(data.projects)\r\n    } catch (err: unknown) {\r\n      setError(err instanceof Error ? err.message : 'Unknown Error')\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const handleCreateProject = async (e: React.FormEvent) => {\r\n    e.preventDefault()\r\n    if (!newProject.name || !newProject.stream_project_id || !newProject.user_email) return\r\n    try {\r\n      setIsCreating(true)\r\n      const response = await fetch('/api/admin/projects', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify(newProject),\r\n      })\r\n      if (!response.ok) {\r\n        const errorData = await response.json()\r\n        throw new Error(errorData.error || 'Failed to create project')\r\n      }\r\n      const data = await response.json()\r\n      setNewProject({ name: '', stream_project_id: '', user_email: '' })\r\n      setIsCreateDialogOpen(false)\r\n      await fetchProjects()\r\n      alert(data.message)\r\n    } catch (err: unknown) {\r\n      alert('Failed to create project: ' + (err instanceof Error ? err.message : 'Unknown error'))\r\n    } finally {\r\n      setIsCreating(false)\r\n    }\r\n  }\r\n\r\n  const handleDeleteProject = async (projectId: string, projectName: string) => {\r\n    const confirmDelete = confirm(\r\n      `Are you sure you want to delete \"${projectName}\"? This action cannot be undone and will delete all associated builds.`\r\n    )\r\n    if (!confirmDelete) return\r\n    try {\r\n      const response = await fetch('/api/admin/projects', {\r\n        method: 'DELETE',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ project_id: projectId }),\r\n      })\r\n      if (!response.ok) {\r\n        const errorData = await response.json()\r\n        throw new Error(errorData.error || 'Failed to delete project')\r\n      }\r\n      alert('Project deleted successfully!')\r\n      await fetchProjects()\r\n    } catch (err: unknown) {\r\n      alert('Failed to delete project: ' + (err instanceof Error ? err.message : 'Unknown error'))\r\n    }\r\n  }\r\n\r\n  const formatDate = (dateString: string) =>\r\n    new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit',\r\n    })\r\n\r\n  // (Optional) Could fetch projects if you want real-time up-to-date, but SSR takes care of initial load\r\n  // useEffect(() => { setProjects(initialProjects) }, [initialProjects])\r\n\r\n  if (!user || profile?.role !== 'platform_admin') {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center\">\r\n        <Card>\r\n          <CardContent className=\"py-8\">\r\n            <div className=\"text-center text-red-600\">\r\n              <p>Access denied. Platform admin role required.</p>\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={() => router.push('/dashboard')}\r\n                className=\"mt-4\"\r\n              >\r\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\r\n                Back to Dashboard\r\n              </Button>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <Navigation />\r\n\r\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        {/* Header */}\r\n        <div className=\"mb-8\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => router.push('/dashboard')}\r\n            className=\"mb-4\"\r\n          >\r\n            <ArrowLeft className=\"h-4 w-4 mr-2\" />\r\n            Back to Dashboard\r\n          </Button>\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <h1 className=\"text-3xl font-bold text-gray-900\">Admin Panel</h1>\r\n              <p className=\"text-gray-600 mt-1\">\r\n                Manage projects and users across the platform\r\n              </p>\r\n            </div>\r\n            <div className=\"flex items-center space-x-4\">\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={() => router.push('/admin/users')}\r\n              >\r\n                <Users className=\"h-4 w-4 mr-2\" />\r\n                Manage Users\r\n              </Button>\r\n              <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>\r\n                <DialogTrigger asChild>\r\n                  <Button>\r\n                    <Plus className=\"h-4 w-4 mr-2\" />\r\n                    Create Project\r\n                  </Button>\r\n                </DialogTrigger>\r\n                <DialogContent>\r\n                  <DialogHeader>\r\n                    <DialogTitle>Create New Project</DialogTitle>\r\n                    <DialogDescription>\r\n                      Create a new project and assign it to a user. The user will be able to upload builds and manage the project.\r\n                    </DialogDescription>\r\n                  </DialogHeader>\r\n                  <form onSubmit={handleCreateProject} className=\"space-y-4\">\r\n                    <div className=\"space-y-2\">\r\n                      <Label htmlFor=\"name\">Project Name</Label>\r\n                      <Input\r\n                        id=\"name\"\r\n                        value={newProject.name}\r\n                        onChange={(e) => setNewProject({ ...newProject, name: e.target.value })}\r\n                        placeholder=\"My Awesome Game\"\r\n                        required\r\n                      />\r\n                    </div>\r\n                    <div className=\"space-y-2\">\r\n                      <Label htmlFor=\"stream_project_id\">Stream Project ID</Label>\r\n                      <Input\r\n                        id=\"stream_project_id\"\r\n                        value={newProject.stream_project_id}\r\n                        onChange={(e) => setNewProject({ ...newProject, stream_project_id: e.target.value })}\r\n                        placeholder=\"stream_project_123\"\r\n                        required\r\n                      />\r\n                    </div>\r\n                    <div className=\"space-y-2\">\r\n                      <Label htmlFor=\"user_email\">User Email</Label>\r\n                      <Input\r\n                        id=\"user_email\"\r\n                        type=\"email\"\r\n                        value={newProject.user_email}\r\n                        onChange={(e) => setNewProject({ ...newProject, user_email: e.target.value })}\r\n                        placeholder=\"<EMAIL>\"\r\n                        required\r\n                      />\r\n                    </div>\r\n                    <div className=\"flex justify-end gap-2\">\r\n                      <Button\r\n                        type=\"button\"\r\n                        variant=\"outline\"\r\n                        onClick={() => setIsCreateDialogOpen(false)}\r\n                      >\r\n                        Cancel\r\n                      </Button>\r\n                      <Button type=\"submit\" disabled={isCreating}>\r\n                        {isCreating ? (\r\n                          <>\r\n                            <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n                            Creating...\r\n                          </>\r\n                        ) : (\r\n                          'Create Project'\r\n                        )}\r\n                      </Button>\r\n                    </div>\r\n                  </form>\r\n                </DialogContent>\r\n              </Dialog>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Stats */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\r\n          <Card>\r\n            <CardContent className=\"p-6\">\r\n              <div className=\"flex items-center\">\r\n                <Package className=\"h-8 w-8 text-blue-500\" />\r\n                <div className=\"ml-4\">\r\n                  <p className=\"text-sm font-medium text-gray-600\">Total Projects</p>\r\n                  <p className=\"text-2xl font-bold text-gray-900\">{projects.length}</p>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n          <Card>\r\n            <CardContent className=\"p-6\">\r\n              <div className=\"flex items-center\">\r\n                <Users className=\"h-8 w-8 text-green-500\" />\r\n                <div className=\"ml-4\">\r\n                  <p className=\"text-sm font-medium text-gray-600\">Active Users</p>\r\n                  <p className=\"text-2xl font-bold text-gray-900\">\r\n                    {new Set(projects.map(p => p.profiles.email)).size}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n          <Card>\r\n            <CardContent className=\"p-6\">\r\n              <div className=\"flex items-center\">\r\n                <Package className=\"h-8 w-8 text-purple-500\" />\r\n                <div className=\"ml-4\">\r\n                  <p className=\"text-sm font-medium text-gray-600\">Total Builds</p>\r\n                  <p className=\"text-2xl font-bold text-gray-900\">\r\n                    {projects.reduce((sum, p) => sum + p.builds.length, 0)}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n\r\n        {/* Projects List */}\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle>All Projects</CardTitle>\r\n            <CardDescription>\r\n              Manage all projects across the platform\r\n            </CardDescription>\r\n          </CardHeader>\r\n          <CardContent>\r\n            {error ? (\r\n              <div className=\"text-center text-red-600 py-8\">\r\n                <p>Error loading projects: {error}</p>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  onClick={fetchProjects}\r\n                  className=\"mt-4\"\r\n                >\r\n                  Retry\r\n                </Button>\r\n              </div>\r\n            ) : projects.length === 0 ? (\r\n              <div className=\"text-center py-8 text-gray-500\">\r\n                <Package className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\r\n                <p>No projects created yet</p>\r\n                <p className=\"text-sm\">Create your first project to get started</p>\r\n              </div>\r\n            ) : (\r\n              <div className=\"space-y-4\">\r\n                {projects.map((project) => (\r\n                  <div\r\n                    key={project.id}\r\n                    className=\"border rounded-lg p-6 hover:bg-gray-50\"\r\n                  >\r\n                    <div className=\"flex items-start justify-between\">\r\n                      <div className=\"flex-1\">\r\n                        <div className=\"flex items-center space-x-3 mb-2\">\r\n                          <h3 className=\"text-lg font-medium text-gray-900\">\r\n                            {project.name}\r\n                          </h3>\r\n                          <Badge variant=\"outline\">\r\n                            {project.stream_project_id}\r\n                          </Badge>\r\n                        </div>\r\n                        <div className=\"flex items-center space-x-6 text-sm text-gray-600 mb-3\">\r\n                          <div className=\"flex items-center\">\r\n                            <UserIcon className=\"h-4 w-4 mr-1\" />\r\n                            {project.profiles.email}\r\n                          </div>\r\n                          <div className=\"flex items-center\">\r\n                            <Package className=\"h-4 w-4 mr-1\" />\r\n                            {project.builds.length} build{project.builds.length !== 1 ? 's' : ''}\r\n                          </div>\r\n                          <div className=\"flex items-center\">\r\n                            <Calendar className=\"h-4 w-4 mr-1\" />\r\n                            Created {formatDate(project.created_at)}\r\n                          </div>\r\n                        </div>\r\n                        {project.builds.length > 0 && (\r\n                          <div className=\"bg-gray-50 p-3 rounded-lg\">\r\n                            <p className=\"text-sm font-medium text-gray-900 mb-1\">\r\n                              Latest Build: {project.builds[0].original_filename || project.builds[0].filename}\r\n                            </p>\r\n                            <p className=\"text-xs text-gray-600\">\r\n                              Version {project.builds[0].version} • {formatDate(project.builds[0].created_at)}\r\n                            </p>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                      {/* Action Buttons */}\r\n                      <div className=\"flex flex-col space-y-2 ml-4\">\r\n                        <Button\r\n                          size=\"sm\"\r\n                          variant=\"outline\"\r\n                          onClick={() => router.push(`/admin/projects/${project.id}`)}\r\n                        >\r\n                          <Eye className=\"h-4 w-4 mr-1\" />\r\n                          View\r\n                        </Button>\r\n                        <Button\r\n                          size=\"sm\"\r\n                          variant=\"outline\"\r\n                          onClick={() => router.push(`/admin/projects/${project.id}`)}\r\n                        >\r\n                          <Edit2 className=\"h-4 w-4 mr-1\" />\r\n                          Edit\r\n                        </Button>\r\n                        <Button\r\n                          size=\"sm\"\r\n                          variant=\"destructive\"\r\n                          onClick={() => handleDeleteProject(project.id, project.name)}\r\n                        >\r\n                          <Trash2 className=\"h-4 w-4 mr-1\" />\r\n                          Delete\r\n                        </Button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </CardContent>\r\n        </Card>\r\n      </main>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAfA;;;;;;;;;;;;AA2Ce,SAAS,YAAY,EAClC,IAAI,EACJ,OAAO,EACP,eAAe,EAKhB;IACC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM;QACN,mBAAmB;QACnB,YAAY;IACd;IAEA,wCAAwC;IACxC,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY,KAAK,QAAQ;QAC3B,EAAE,OAAO,KAAc;YACrB,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,EAAE,cAAc;QAChB,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,iBAAiB,IAAI,CAAC,WAAW,UAAU,EAAE;QACjF,IAAI;YACF,cAAc;YACd,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,cAAc;gBAAE,MAAM;gBAAI,mBAAmB;gBAAI,YAAY;YAAG;YAChE,sBAAsB;YACtB,MAAM;YACN,MAAM,KAAK,OAAO;QACpB,EAAE,OAAO,KAAc;YACrB,MAAM,+BAA+B,CAAC,eAAe,QAAQ,IAAI,OAAO,GAAG,eAAe;QAC5F,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,sBAAsB,OAAO,WAAmB;QACpD,MAAM,gBAAgB,QACpB,CAAC,iCAAiC,EAAE,YAAY,sEAAsE,CAAC;QAEzH,IAAI,CAAC,eAAe;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,YAAY;gBAAU;YAC/C;YACA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YACA,MAAM;YACN,MAAM;QACR,EAAE,OAAO,KAAc;YACrB,MAAM,+BAA+B,CAAC,eAAe,QAAQ,IAAI,OAAO,GAAG,eAAe;QAC5F;IACF;IAEA,MAAM,aAAa,CAAC,aAClB,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YAC/C,MAAM;YAAW,OAAO;YAAS,KAAK;YAAW,MAAM;YAAW,QAAQ;QAC5E;IAEF,uGAAuG;IACvG,uEAAuE;IAEvE,IAAI,CAAC,QAAQ,SAAS,SAAS,kBAAkB;QAC/C,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAE;;;;;;0CACH,8OAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQpD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yHAAA,CAAA,aAAU;;;;;0BAEX,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;kDAIpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,OAAO,IAAI,CAAC;;kEAE3B,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGpC,8OAAC,2HAAA,CAAA,SAAM;gDAAC,MAAM;gDAAoB,cAAc;;kEAC9C,8OAAC,2HAAA,CAAA,gBAAa;wDAAC,OAAO;kEACpB,cAAA,8OAAC,2HAAA,CAAA,SAAM;;8EACL,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;kEAIrC,8OAAC,2HAAA,CAAA,gBAAa;;0EACZ,8OAAC,2HAAA,CAAA,eAAY;;kFACX,8OAAC,2HAAA,CAAA,cAAW;kFAAC;;;;;;kFACb,8OAAC,2HAAA,CAAA,oBAAiB;kFAAC;;;;;;;;;;;;0EAIrB,8OAAC;gEAAK,UAAU;gEAAqB,WAAU;;kFAC7C,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,0HAAA,CAAA,QAAK;gFAAC,SAAQ;0FAAO;;;;;;0FACtB,8OAAC,0HAAA,CAAA,QAAK;gFACJ,IAAG;gFACH,OAAO,WAAW,IAAI;gFACtB,UAAU,CAAC,IAAM,cAAc;wFAAE,GAAG,UAAU;wFAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oFAAC;gFACrE,aAAY;gFACZ,QAAQ;;;;;;;;;;;;kFAGZ,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,0HAAA,CAAA,QAAK;gFAAC,SAAQ;0FAAoB;;;;;;0FACnC,8OAAC,0HAAA,CAAA,QAAK;gFACJ,IAAG;gFACH,OAAO,WAAW,iBAAiB;gFACnC,UAAU,CAAC,IAAM,cAAc;wFAAE,GAAG,UAAU;wFAAE,mBAAmB,EAAE,MAAM,CAAC,KAAK;oFAAC;gFAClF,aAAY;gFACZ,QAAQ;;;;;;;;;;;;kFAGZ,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,0HAAA,CAAA,QAAK;gFAAC,SAAQ;0FAAa;;;;;;0FAC5B,8OAAC,0HAAA,CAAA,QAAK;gFACJ,IAAG;gFACH,MAAK;gFACL,OAAO,WAAW,UAAU;gFAC5B,UAAU,CAAC,IAAM,cAAc;wFAAE,GAAG,UAAU;wFAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oFAAC;gFAC3E,aAAY;gFACZ,QAAQ;;;;;;;;;;;;kFAGZ,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,2HAAA,CAAA,SAAM;gFACL,MAAK;gFACL,SAAQ;gFACR,SAAS,IAAM,sBAAsB;0FACtC;;;;;;0FAGD,8OAAC,2HAAA,CAAA,SAAM;gFAAC,MAAK;gFAAS,UAAU;0FAC7B,2BACC;;sGACE,8OAAC,iNAAA,CAAA,UAAO;4FAAC,WAAU;;;;;;wFAA8B;;mGAInD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,yHAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,yHAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEAAoC,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKxE,8OAAC,yHAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,yHAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEACV,IAAI,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,KAAK,GAAG,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM5D,8OAAC,yHAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,yHAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEACV,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAShE,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;;kDACT,8OAAC,yHAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,yHAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,yHAAA,CAAA,cAAW;0CACT,sBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAE;gDAAyB;;;;;;;sDAC5B,8OAAC,2HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;2CAID,SAAS,MAAM,KAAK,kBACtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;sDAAE;;;;;;sDACH,8OAAC;4CAAE,WAAU;sDAAU;;;;;;;;;;;yDAGzB,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4CAEC,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFACX,QAAQ,IAAI;;;;;;kFAEf,8OAAC,0HAAA,CAAA,QAAK;wEAAC,SAAQ;kFACZ,QAAQ,iBAAiB;;;;;;;;;;;;0EAG9B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,kMAAA,CAAA,OAAQ;gFAAC,WAAU;;;;;;4EACnB,QAAQ,QAAQ,CAAC,KAAK;;;;;;;kFAEzB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,wMAAA,CAAA,UAAO;gFAAC,WAAU;;;;;;4EAClB,QAAQ,MAAM,CAAC,MAAM;4EAAC;4EAAO,QAAQ,MAAM,CAAC,MAAM,KAAK,IAAI,MAAM;;;;;;;kFAEpE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,0MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;4EAAiB;4EAC5B,WAAW,QAAQ,UAAU;;;;;;;;;;;;;4DAGzC,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACvB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;;4EAAyC;4EACrC,QAAQ,MAAM,CAAC,EAAE,CAAC,iBAAiB,IAAI,QAAQ,MAAM,CAAC,EAAE,CAAC,QAAQ;;;;;;;kFAElF,8OAAC;wEAAE,WAAU;;4EAAwB;4EAC1B,QAAQ,MAAM,CAAC,EAAE,CAAC,OAAO;4EAAC;4EAAI,WAAW,QAAQ,MAAM,CAAC,EAAE,CAAC,UAAU;;;;;;;;;;;;;;;;;;;kEAMtF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2HAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,EAAE;;kFAE1D,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGlC,8OAAC,2HAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,EAAE;;kFAE1D,8OAAC,kMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGpC,8OAAC,2HAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,oBAAoB,QAAQ,EAAE,EAAE,QAAQ,IAAI;;kFAE3D,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;2CA7DpC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2EnC", "debugId": null}}]}