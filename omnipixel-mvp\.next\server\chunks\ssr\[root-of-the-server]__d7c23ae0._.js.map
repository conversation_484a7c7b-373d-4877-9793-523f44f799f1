{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/app/dashboard/client.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/dashboard/client.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/dashboard/client.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/app/dashboard/client.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/dashboard/client.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/dashboard/client.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwQ,GACrS,sCACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\r\nimport { cookies } from 'next/headers'\r\n\r\nexport async function createClient(url?:string, key?:string) {\r\n  const cookieStore = await cookies()\r\n  url = url ?? process.env.NEXT_PUBLIC_SUPABASE_URL!\r\n  key = key ?? process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY!\r\n\r\n  return createServerClient(\r\n    url,\r\n    key,\r\n    {\r\n      cookies: {\r\n        getAll() {\r\n          return cookieStore.getAll()\r\n        },\r\n        setAll(cookiesToSet) {\r\n          try {\r\n            cookiesToSet.forEach(({ name, value, options }) =>\r\n              cookieStore.set(name, value, options)\r\n            )\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  )\r\n}"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe,aAAa,GAAW,EAAE,GAAW;IACzD,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM;IACN,MAAM;IAEN,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EACtB,KACA,KACA;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/app/dashboard/page.tsx"], "sourcesContent": ["// app/dashboard/page.tsx\nimport { redirect } from 'next/navigation'\nimport DashboardClient from './client'\nimport { createClient } from '@/utils/supabase/server'\n\nexport default async function DashboardPage() {\n  const supabase = await createClient();\n\n  // Get the user\n  const { data: { user } } = await supabase.auth.getUser()\n  if (!user) {\n    redirect('/login')\n  }\n\n  // Get the profile (assuming you have a profiles table)\n  const { data: profile } = await supabase\n    .from('profiles')\n    .select('*')\n    .eq('id', user.id)\n    .single()\n\n  // Fetch projects for user (handle admin logic if needed)\n  // If user is admin, fetch all. Otherwise, only their projects.\n  let projects = []\n  if (profile?.role === 'platform_admin') {\n    const { data } = await supabase\n      .from('projects')\n      .select(`\n        *,\n        builds (\n          id,\n          filename,\n          original_filename,\n          version,\n          created_at,\n          status,\n          is_current\n        )\n      `)\n      .order('created_at', { ascending: false })\n    projects = data || []\n  } else {\n    const { data } = await supabase\n      .from('projects')\n      .select(`\n        *,\n        builds (\n          id,\n          filename,\n          original_filename,\n          version,\n          created_at,\n          status,\n          is_current\n        )\n      `)\n      .eq('user_id', user.id)\n      .order('created_at', { ascending: false })\n    projects = data || []\n  }\n\n  return (\n    <DashboardClient\n      user={user}\n      profile={profile}\n      projects={projects}\n    />\n  )\n}\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;;AACzB;AAAA;AACA;AACA;;;;;AAEe,eAAe;IAC5B,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,eAAe;IACf,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,uDAAuD;IACvD,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;IAET,yDAAyD;IACzD,+DAA+D;IAC/D,IAAI,WAAW,EAAE;IACjB,IAAI,SAAS,SAAS,kBAAkB;QACtC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,SACpB,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;;;;;;;;;MAWT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAC1C,WAAW,QAAQ,EAAE;IACvB,OAAO;QACL,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,SACpB,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;;;;;;;;;MAWT,CAAC,EACA,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAC1C,WAAW,QAAQ,EAAE;IACvB;IAEA,qBACE,8OAAC,2HAAA,CAAA,UAAe;QACd,MAAM;QACN,SAAS;QACT,UAAU;;;;;;AAGhB", "debugId": null}}]}