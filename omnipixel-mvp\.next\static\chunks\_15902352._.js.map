{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,KAEgC;QAFhC,EACrB,GAAG,OACkD,GAFhC;IAGrB,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAGgC;QAHhC,EACrB,SAAS,EACT,GAAG,OACkD,GAHhC;IAIrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,KAGgC;QAHhC,EACzB,SAAS,EACT,GAAG,OACsD,GAHhC;IAIzB,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 509, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/stream-player.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { StreamPixelApplication } from 'streampixelsdk'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'\nimport {\n  Play,\n  Square,\n  Volume2,\n  VolumeX,\n  Maximize,\n  Loader2,\n  AlertCircle,\n  ExternalLink,\n  Code,\n  Lock,\n  Unlock\n} from 'lucide-react'\n\ninterface StreamPlayerProps {\n  projectId: string\n  buildId?: string\n  className?: string\n  config?: StreamPlayerConfig\n  showControls?: boolean\n  showHeader?: boolean\n  showEmbedButton?: boolean\n  width?: number\n  height?: number\n  isEmbedded?: boolean\n  enableIframeComms?: boolean\n}\n\ninterface StreamPlayerConfig {\n  autoConnect?: boolean\n  touchInput?: boolean\n  keyBoardInput?: boolean\n  resolutionMode?: string\n  maxStreamQuality?: string\n  primaryCodec?: string\n  fallBackCodec?: string\n  isPasswordProtected?: boolean\n  password?: string\n  loadingMessage?: string\n  connectingMessage?: string\n  disconnectedMessage?: string\n  reconnectingMessage?: string\n  errorMessage?: string\n  connectButtonText?: string\n}\n\nexport function StreamPlayer({\n  projectId,\n  buildId,\n  className = '',\n  config: propConfig,\n  showControls = true,\n  showHeader = true,\n  showEmbedButton = true,\n  width = 1280,\n  height = 720,\n  isEmbedded = false,\n  enableIframeComms = false\n}: StreamPlayerProps) {\n  const [isLoading, setIsLoading] = useState(false)\n  const [isPlaying, setIsPlaying] = useState(false)\n  const [isMuted, setIsMuted] = useState(false)\n  const [streamProjectId, setStreamProjectId] = useState<string | null>(null)\n  const [config, setConfig] = useState<StreamPlayerConfig | null>(null)\n  const [projectData, setProjectData] = useState<any>(null)\n  const [isFullscreen, setIsFullscreen] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const [streamStatus, setStreamStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected')\n  const [loadingMessage, setLoadingMessage] = useState<string>('Loading stream...')\n  const [isVideoReady, setIsVideoReady] = useState(false)\n  const [isPasswordPromptOpen, setIsPasswordPromptOpen] = useState(false)\n  const [enteredPassword, setEnteredPassword] = useState('')\n  const [isPasswordValid, setIsPasswordValid] = useState(false)\n  const [showEmbedDialog, setShowEmbedDialog] = useState(false)\n\n  const containerRef = useRef<HTMLDivElement>(null) // for overlays/background\n  const sdkMountRef = useRef<HTMLDivElement>(null)   // ONLY for SDK-injected DOM\n\n  const streamRef = useRef<any>(null)\n  const playerRef = useRef<any>(null)\n  const uiControlRef = useRef<any>(null)\n  const isInitializedRef = useRef<boolean>(false)\n  const isInitializingRef = useRef<boolean>(false)\n\n  // Project data and config loader\n  useEffect(() => {\n    const fetchProjectData = async () => {\n      try {\n        setIsLoading(false)\n        setError(null)\n        const response = await fetch(`/api/projects/${projectId}`)\n        if (!response.ok) throw new Error('Failed to fetch project data')\n        const responseData = await response.json()\n        const data = responseData.project || responseData\n        setProjectData(data)\n        setStreamProjectId(data.stream_project_id)\n        const finalConfig = propConfig || data.config || {}\n        setConfig(finalConfig)\n        if (!data.stream_project_id) throw new Error('No StreamPixel ID found in project data')\n      } catch (err) {\n        setError('Failed to load project configuration')\n      } finally {\n        setIsLoading(false)\n      }\n    }\n    if (projectId) fetchProjectData()\n  }, [projectId, propConfig])\n\n  // Clean up SDK DOM on unmount/disconnect\n  useEffect(() => {\n    return () => { cleanup() }\n  }, [])\n\n  // Auto-connect for embedded mode\n  useEffect(() => {\n    if (config?.autoConnect && streamProjectId && !isPlaying && !isInitializingRef.current) {\n      console.log('🚀 Auto-connecting stream...', {\n        autoConnect: config?.autoConnect,\n        streamProjectId,\n        isPlaying,\n        isInitializing: isInitializingRef.current\n      })\n      handlePlay()\n    }\n  }, [config?.autoConnect, streamProjectId, isPlaying])\n\n  // Prepare messages\n  const messages = {\n    loading: config?.loadingMessage ?? 'Loading stream...',\n    connecting: config?.connectingMessage ?? 'Connecting to stream...',\n    disconnected: config?.disconnectedMessage ?? 'Stream disconnected',\n    reconnecting: config?.reconnectingMessage ?? 'Reconnecting...',\n    error: config?.errorMessage ?? 'Stream error occurred',\n    connectButton: config?.connectButtonText ?? 'Connect to Stream'\n  }\n\n  const createStreamConfig = () => {\n    if (!streamProjectId || !config) return null\n    return {\n      AutoConnect: true,\n      appId: streamProjectId,\n      touchInput: config?.touchInput ?? true,\n      keyBoardInput: config?.keyBoardInput ?? true,\n      resolutionMode: (config?.resolutionMode as \"Dynamic Resolution Mode\" | \"Fixed Resolution Mode\" | \"Crop on Resize Mode\") ?? \"Dynamic Resolution Mode\",\n      maxStreamQuality: config?.maxStreamQuality ?? \"1080p (1920x1080)\",\n      primaryCodec: (config?.primaryCodec as \"H264\" | \"VP8\" | \"AV1\" | \"VP9\") ?? \"H264\",\n      fallBackCodec: (config?.fallBackCodec as \"H264\" | \"VP8\" | \"AV1\" | \"VP9\") ?? \"VP8\"\n    }\n  }\n\n const initializeStream = async () => {\n  if (isInitializingRef.current) return // guard against parallel inits\n  isInitializingRef.current = true\n  setIsLoading(true)\n  setError(null)\n\n\n\n  try {\n    const streamConfig = createStreamConfig()\n    if (!streamConfig) throw new Error('Cannot create stream config: missing required data')\n    const { appStream, pixelStreaming, UIControl } = await StreamPixelApplication(streamConfig)\n    streamRef.current = appStream\n    playerRef.current = pixelStreaming\n    uiControlRef.current = UIControl\n\n    // Inject SDK DOM\n    if (sdkMountRef.current && appStream.rootElement) {\n      if (appStream.rootElement.parentNode) {\n        appStream.rootElement.parentNode.removeChild(appStream.rootElement)\n      }\n      sdkMountRef.current.innerHTML = ''\n      sdkMountRef.current.appendChild(appStream.rootElement)\n    }\n\n    setupEventListeners()\n    isInitializedRef.current = true\n    setIsLoading(true)\n  } catch (err: any) {\n    setError(err.message || 'Failed to initialize stream')\n    setIsLoading(false)\n  } finally {\n    isInitializingRef.current = false\n  }\n}\n  const cleanup = () => {\n   try {\n  \n\n\n    if (streamRef.current?.stream?.disconnect) streamRef.current.stream.disconnect()\n    if (playerRef.current?.disconnect) playerRef.current.disconnect()\n\n  \n    if (sdkMountRef.current) sdkMountRef.current.innerHTML = ''\n  } catch (err) {}\n  // Nullify refs\n  streamRef.current = null\n  playerRef.current = null\n  uiControlRef.current = null\n  isInitializedRef.current = false\n  isInitializingRef.current = false\n  }\n\n  // Iframe communication system\n  const sendMessageToParent = (type: string, data: any = {}) => {\n    if (enableIframeComms && typeof window !== 'undefined' && window.parent !== window) {\n      try {\n        window.parent.postMessage({\n          type: `omnipixel-${type}`,\n          projectId,\n          buildId,\n          timestamp: new Date().toISOString(),\n          ...data\n        }, '*')\n      } catch (error) {\n        console.warn('Failed to send message to parent:', error)\n      }\n    }\n  }\n\n  // Listen for messages from parent iframe\n  useEffect(() => {\n    if (!enableIframeComms || typeof window === 'undefined') return\n\n    const handleMessage = (event: MessageEvent) => {\n      if (!event.data?.type?.startsWith('omnipixel-')) return\n\n      const { type, data } = event.data\n      console.log('📨 Received message from parent:', type, data)\n\n      switch (type) {\n        case 'omnipixel-connect':\n          if (!isPlaying) {\n            handlePlay()\n          }\n          break\n        case 'omnipixel-disconnect':\n          if (isPlaying) {\n            handlePause()\n          }\n          break\n        case 'omnipixel-mute':\n          setIsMuted(true)\n          break\n        case 'omnipixel-unmute':\n          setIsMuted(false)\n          break\n        case 'omnipixel-fullscreen':\n          setIsFullscreen(true)\n          break\n        case 'omnipixel-exit-fullscreen':\n          setIsFullscreen(false)\n          break\n        case 'omnipixel-send-input':\n          // Forward input to StreamPixel\n          if (playerRef.current && data.input) {\n            // Handle different input types\n            if (data.input.type === 'keyboard') {\n              // Send keyboard input to StreamPixel\n            } else if (data.input.type === 'mouse') {\n              // Send mouse input to StreamPixel\n            }\n          }\n          break\n      }\n    }\n\n    window.addEventListener('message', handleMessage)\n    return () => window.removeEventListener('message', handleMessage)\n  }, [enableIframeComms, isPlaying, projectId, buildId])\n\n  // Send status updates to parent\n  useEffect(() => {\n    if (enableIframeComms) {\n      sendMessageToParent('status-change', {\n        isLoading,\n        isPlaying,\n        streamStatus,\n        error,\n        isMuted,\n        isFullscreen\n      })\n    }\n  }, [enableIframeComms, isLoading, isPlaying, streamStatus, error, isMuted, isFullscreen])\n\n  // Event listeners (simplified for clarity, you can add more as needed)\n  const setupEventListeners = () => {\n    if (!streamRef.current) return\n\n    streamRef.current.onConnectAction = () => {\n      console.log('🔗 StreamPixel: Connect action triggered')\n      setStreamStatus('connecting')\n      setIsLoading(true)\n      setIsVideoReady(false)\n      setLoadingMessage(config?.connectingMessage ?? 'Connecting to stream...')\n      sendMessageToParent('connect-started')\n    }\n\n    streamRef.current.onWebRtcConnecting = () => {\n      console.log('🌐 StreamPixel: WebRTC connecting')\n      setStreamStatus('connecting')\n      setLoadingMessage(config?.connectingMessage ?? 'Establishing connection...')\n      sendMessageToParent('webrtc-connecting')\n    }\n\n    streamRef.current.onWebRtcConnected = () => {\n      console.log('✅ StreamPixel: WebRTC connected')\n      setStreamStatus('connecting')\n      setLoadingMessage('Initializing video stream...')\n      // Don't hide loading yet - wait for video to be ready\n      sendMessageToParent('webrtc-connected')\n    }\n\n    streamRef.current.onVideoInitialized = () => {\n      console.log('🎥 StreamPixel: Video initialized and ready')\n      console.log('🔄 Setting loading state to false - video ready')\n      setIsLoading(false) // Hide loading when video is actually ready\n      setIsVideoReady(true)\n      setStreamStatus('connected')\n      setIsPlaying(true)\n      sendMessageToParent('video-initialized')\n    }\n\n    streamRef.current.onDisconnect = () => {\n      console.log('🔌 StreamPixel: Disconnected')\n      setIsPlaying(false)\n      setIsLoading(false)\n      setIsVideoReady(false)\n      setStreamStatus('disconnected')\n      setLoadingMessage(config?.disconnectedMessage ?? 'Stream disconnected')\n      sendMessageToParent('disconnected')\n\n      // Use requestAnimationFrame for React-safe cleanup\n      requestAnimationFrame(() => {\n        cleanup()\n      })\n    }\n\n    // Additional StreamPixel events for iframe communication\n    if (streamRef.current.onDataChannelOpen) {\n      streamRef.current.onDataChannelOpen = () => {\n        console.log('📡 StreamPixel: Data channel opened')\n        sendMessageToParent('data-channel-open')\n      }\n    }\n\n    if (streamRef.current.onDataChannelMessage) {\n      streamRef.current.onDataChannelMessage = (message: any) => {\n        console.log('📨 StreamPixel: Data channel message', message)\n        sendMessageToParent('data-channel-message', { message })\n      }\n    }\n\n    // Unreal Engine specific events\n    if (streamRef.current.onUnrealMessage) {\n      streamRef.current.onUnrealMessage = (message: any) => {\n        console.log('🎮 Unreal Engine message:', message)\n        sendMessageToParent('unreal-message', { message })\n      }\n    }\n  }\n\n  // Control handlers\n  const handlePlay = async () => {\n    console.log('▶️ Play button clicked')\n    sendMessageToParent('play-requested')\n\n    console.log('🔄 Setting loading state to true')\n    setIsLoading(true)\n    setIsVideoReady(false)\n    setLoadingMessage(config?.loadingMessage ?? 'Initializing stream...')\n\n    cleanup()\n    await initializeStream()\n    setStreamStatus('connecting')\n    setIsPlaying(true)\n  }\n\n  const handlePause = () => {\n    console.log('⏸️ Pause button clicked')\n    sendMessageToParent('pause-requested')\n\n    playerRef.current?.disconnect()\n    streamRef.current?.stream?.disconnect()\n\n    // Use requestAnimationFrame for React-safe state updates\n    requestAnimationFrame(() => {\n      setIsPlaying(false)\n      setStreamStatus('disconnected')\n    })\n\n    cleanup()\n    window.location.reload();\n  }\n\n  const handleMute = () => {\n    console.log('🔇 Mute button clicked')\n    if (uiControlRef.current) {\n      uiControlRef.current.toggleAudio()\n      setIsMuted(!isMuted)\n      sendMessageToParent('mute-toggled', { isMuted: !isMuted })\n    }\n  }\n  const handleFullscreen = () => {\n    if (!containerRef.current) return\n    if (!isFullscreen) {\n      containerRef.current.requestFullscreen?.()\n    } else {\n      document.exitFullscreen?.()\n    }\n    setIsFullscreen(!isFullscreen)\n  }\n\n  // Password dialog handler\n  const handlePasswordSubmit = () => {\n    if (enteredPassword === config?.password) {\n      setIsPasswordValid(true)\n      setIsPasswordPromptOpen(false)\n    } else {\n      setError('Invalid password')\n    }\n  }\n\n  // Embed code utilities\n  const generateEmbedCode = () => {\n    const origin = typeof window !== 'undefined' ? window.location.origin : 'https://your-domain.com'\n    const params = new URLSearchParams()\n\n    // Add control visibility parameters\n    if (!showControls) params.set('hideControls', 'true')\n    if (!showHeader) params.set('hideHeader', 'true')\n    if (!showEmbedButton) params.set('hideEmbedButton', 'true')\n    if (config?.autoConnect) params.set('autoConnect', 'true')\n\n    const queryString = params.toString()\n    const embedUrl = `${origin}/embed/${projectId}${queryString ? `?${queryString}` : ''}`\n\n    return `<!-- OmniPixel Interactive Stream Embed -->\n<iframe\n  src=\"${embedUrl}\"\n  width=\"${width}\"\n  height=\"${height}\"\n  frameborder=\"0\"\n  allowfullscreen\n  allow=\"camera; microphone; fullscreen\"\n  style=\"border: none; border-radius: 8px;\">\n</iframe>\n\n<!-- Optional: Listen for iframe events -->\n<script>\nwindow.addEventListener('message', function(event) {\n  if (event.data?.type?.startsWith('omnipixel-')) {\n    console.log('Stream event:', event.data.type, event.data);\n\n    // Handle specific events\n    switch(event.data.type) {\n      case 'omnipixel-status-change':\n        console.log('Stream status:', event.data.streamStatus);\n        break;\n      case 'omnipixel-webrtc-connected':\n        console.log('Stream connected successfully');\n        break;\n      case 'omnipixel-unreal-message':\n        console.log('Unreal Engine message:', event.data.message);\n        break;\n    }\n  }\n});\n\n// Optional: Send commands to the stream\nfunction connectStream() {\n  document.querySelector('iframe').contentWindow.postMessage({\n    type: 'omnipixel-connect'\n  }, '*');\n}\n\nfunction disconnectStream() {\n  document.querySelector('iframe').contentWindow.postMessage({\n    type: 'omnipixel-disconnect'\n  }, '*');\n}\n</script>`\n  }\n  const copyEmbedCode = async () => {\n    try {\n      await navigator.clipboard.writeText(generateEmbedCode())\n      alert('Embed code copied to clipboard!')\n    } catch { }\n  }\n  const openInNewTab = () => {\n    if (typeof window !== 'undefined') {\n      const url = `${window.location.origin}/projects/${projectId}`\n      window.open(url, '_blank', 'noopener,noreferrer')\n    }\n  }\n\n  const getStatusColor = () => {\n    switch (streamStatus) {\n      case 'connected': return 'bg-green-100 text-green-800'\n      case 'connecting': return 'bg-yellow-100 text-yellow-800'\n      case 'disconnected': return 'bg-gray-100 text-gray-800'\n      case 'error': return 'bg-red-100 text-red-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n  const getStatusText = () => {\n    switch (streamStatus) {\n      case 'connected': return 'Connected'\n      case 'connecting': return 'Connecting...'\n      case 'disconnected': return 'Disconnected'\n      case 'error': return 'Error'\n      default: return 'Unknown'\n    }\n  }\n\n  // Password protection UI\n  if (config?.isPasswordProtected && !isPasswordValid && isPasswordPromptOpen) {\n    return (\n      <Card className={className}>\n        <CardContent className=\"pt-6\">\n          <div className=\"text-center space-y-4\">\n            <Lock className=\"h-12 w-12 mx-auto text-gray-400\" />\n            <h3 className=\"text-lg font-medium\">Password Protected</h3>\n            <p className=\"text-gray-600\">This stream requires a password to access.</p>\n            <div className=\"max-w-sm mx-auto space-y-3\">\n              <Input\n                type=\"password\"\n                placeholder=\"Enter password\"\n                value={enteredPassword}\n                onChange={(e) => setEnteredPassword(e.target.value)}\n                onKeyDown={(e) => e.key === 'Enter' && handlePasswordSubmit()}\n              />\n              <Button onClick={handlePasswordSubmit} className=\"w-full\">\n                <Unlock className=\"h-4 w-4 mr-2\" />\n                Access Stream\n              </Button>\n            </div>\n            {error && <p className=\"text-sm text-red-600\">{error}</p>}\n          </div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  // Full-screen embedded mode\n  if (isEmbedded && enableIframeComms) {\n    return (\n      <div className={`relative w-full h-full ${className}`}>\n        {/* Stream container */}\n        <div\n          ref={containerRef}\n          className=\"absolute inset-0 bg-black\"\n        >\n          {/* StreamPixel SDK mount point */}\n          <div\n            ref={sdkMountRef}\n            className=\"absolute inset-0 w-full h-full\"\n          />\n          {/* Loading overlay */}\n          {(isLoading || streamStatus === 'connecting') && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-900/95 z-20\">\n              <div className=\"text-center text-white\">\n                <Loader2 className=\"h-12 w-12 animate-spin mx-auto mb-4\" />\n                <p className=\"text-lg font-medium mb-2\">{loadingMessage}</p>\n                <p className=\"text-sm opacity-75\">Status: {getStatusText()}</p>\n              </div>\n            </div>\n          )}\n\n          {/* Error overlay */}\n          {error && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-red-900 z-20\">\n              <div className=\"text-center text-white p-4\">\n                <AlertCircle className=\"h-8 w-8 mx-auto mb-2\" />\n                <p>{error}</p>\n              </div>\n            </div>\n          )}\n\n          {/* Floating control buttons - no bar background */}\n          {showControls && (\n            <>\n              {/* Status badges - top left */}\n              <div className=\"absolute top-4 left-4 z-30 flex items-center space-x-2\">\n                <Badge className={getStatusColor()}>{getStatusText()}</Badge>\n                {buildId && (\n                  <Badge variant=\"outline\" className=\"text-white border-white/50 bg-black/70 backdrop-blur-sm\">\n                    Build: {buildId.slice(0, 8)}...\n                  </Badge>\n                )}\n              </div>\n\n              {/* Control buttons - bottom right */}\n              <div className=\"absolute bottom-4 right-4 z-30 flex items-center space-x-2\">\n                {!isPlaying ? (\n                  <Button\n                    onClick={handlePlay}\n                    size=\"sm\"\n                    className=\"bg-green-600/90 hover:bg-green-700 text-white font-medium px-4 backdrop-blur-sm shadow-lg\"\n                  >\n                    <Play className=\"h-4 w-4 mr-2\" />\n                    Connect\n                  </Button>\n                ) : (\n                  <Button\n                    onClick={handlePause}\n                    variant=\"destructive\"\n                    size=\"sm\"\n                    className=\"bg-red-600/90 hover:bg-red-700 text-white font-medium px-4 backdrop-blur-sm shadow-lg\"\n                  >\n                    <Square className=\"h-4 w-4 mr-2\" />\n                    Disconnect\n                  </Button>\n                )}\n\n                <Button\n                  onClick={handleMute}\n                  variant=\"outline\"\n                  size=\"sm\"\n                  className=\"text-white border-white/50 bg-black/70 hover:bg-black/80 hover:text-white backdrop-blur-sm shadow-lg\"\n                >\n                  {isMuted ? <VolumeX className=\"h-4 w-4\" /> : <Volume2 className=\"h-4 w-4\" />}\n                </Button>\n\n                <Button\n                  onClick={handleFullscreen}\n                  variant=\"outline\"\n                  size=\"sm\"\n                  className=\"text-white border-white/50 bg-black/70 hover:bg-black/80 hover:text-white backdrop-blur-sm shadow-lg\"\n                >\n                  <Maximize className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </>\n          )}\n        </div>\n      </div>\n    )\n  }\n\n  // Standard card mode\n  return (\n    <Card className={className}>\n      {showHeader && (\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <CardTitle>Interactive Stream</CardTitle>\n              <CardDescription>Real-time interactive streaming experience</CardDescription>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Badge className={getStatusColor()}>{getStatusText()}</Badge>\n              {buildId && (\n                <Badge variant=\"outline\">\n                  Build: {buildId.slice(0, 8)}...\n                </Badge>\n              )}\n              {showEmbedButton && (\n                <Dialog open={showEmbedDialog} onOpenChange={setShowEmbedDialog}>\n                  <DialogTrigger asChild>\n                    <Button variant=\"outline\" size=\"sm\">\n                      <Code className=\"h-4 w-4 mr-2\" />\n                      Embed\n                    </Button>\n                  </DialogTrigger>\n                  <DialogContent>\n                    <DialogHeader>\n                      <DialogTitle>Embed Stream</DialogTitle>\n                      <DialogDescription>Copy this code to embed the stream in your website</DialogDescription>\n                    </DialogHeader>\n                    <div className=\"space-y-4\">\n                      <div>\n                        <label className=\"text-sm font-medium\">Embed Code:</label>\n                        <textarea\n                          className=\"w-full mt-1 p-2 border rounded text-sm font-mono\"\n                          rows={6}\n                          readOnly\n                          value={generateEmbedCode()}\n                        />\n                      </div>\n                      <Button onClick={copyEmbedCode} className=\"w-full\">Copy Embed Code</Button>\n                    </div>\n                  </DialogContent>\n                </Dialog>\n              )}\n            </div>\n          </div>\n        </CardHeader>\n      )}\n\n      <CardContent>\n        {error && (\n          <div className=\"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg\">\n            <div className=\"flex items-center\">\n              <AlertCircle className=\"h-5 w-5 text-red-500 mr-2\" />\n              <p className=\"text-sm text-red-700\">{error}</p>\n            </div>\n          </div>\n        )}\n\n        <div\n          ref={containerRef}\n          className=\"relative bg-black rounded-lg overflow-hidden\"\n          style={{\n            width: '100%',\n            aspectRatio: `${width}/${height}`,\n            minHeight: '400px'\n          }}\n        >\n          {/* overlays */}\n          {(isLoading || streamStatus === 'connecting') && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-900/95 z-10\">\n              <div className=\"text-center text-white\">\n                <Loader2 className=\"h-12 w-12 animate-spin mx-auto mb-4\" />\n                <p className=\"text-lg font-medium mb-2\">{loadingMessage}</p>\n                <p className=\"text-sm opacity-75\">Status: {getStatusText()}</p>\n              </div>\n            </div>\n          )}\n          {!isLoading && !isPlaying && !error && streamStatus === 'disconnected' && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-900 z-10\">\n              <div className=\"text-center text-white space-y-4\">\n                <Play className=\"h-16 w-16 mx-auto opacity-50\" />\n                <div>\n                  <p className=\"text-lg mb-2\">Ready to Connect</p>\n                  <p className=\"text-sm opacity-75 mb-4\">{messages.disconnected}</p>\n                  {!config?.autoConnect && (\n                    <Button onClick={handlePlay} size=\"lg\">\n                      <Play className=\"h-5 w-5 mr-2\" />\n                      {messages.connectButton}\n                    </Button>\n                  )}\n                </div>\n              </div>\n            </div>\n          )}\n          {streamStatus === 'error' && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-900 z-10\">\n              <div className=\"text-center text-white\">\n                <AlertCircle className=\"h-16 w-16 mx-auto mb-4 text-red-500\" />\n                <p className=\"text-lg mb-2\">Connection Error</p>\n                <p className=\"text-sm opacity-75\">{error || messages.error}</p>\n              </div>\n            </div>\n          )}\n\n          {/* SDK DOM mounts ONLY here */}\n          <div\n            ref={sdkMountRef}\n            className=\"absolute inset-0 w-full h-full\"\n            style={{ zIndex: 0, pointerEvents: isLoading ? 'none' : 'auto' }}\n          />\n        </div>\n\n        {/* Controls */}\n        {showControls && (\n          <div className=\"mt-4 flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              {streamStatus === 'disconnected' || streamStatus === 'error' ? (\n                <Button onClick={handlePlay} disabled={isLoading}>\n                  <Play className=\"h-4 w-4\" />\n                  Connect\n                </Button>\n              ) : streamStatus === 'connecting' ? (\n                <Button disabled>\n                  <Loader2 className=\"h-4 w-4 animate-spin\" />\n                  {messages.connecting}\n                </Button>\n              ) : (\n                <Button variant=\"outline\" onClick={handlePause}>\n                  <Square className=\"h-4 w-4\" />\n                  Disconnect\n                </Button>\n              )}\n              <Button variant=\"outline\" onClick={handleMute}>\n                {isMuted ? (\n                  <VolumeX className=\"h-4 w-4\" />\n                ) : (\n                  <Volume2 className=\"h-4 w-4\" />\n                )}\n              </Button>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Button variant=\"outline\" size=\"sm\" onClick={handleFullscreen}>\n                <Maximize className=\"h-4 w-4\" />\n              </Button>\n              <Button variant=\"outline\" size=\"sm\" onClick={openInNewTab} title=\"Open in new tab\">\n                <ExternalLink className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        )}\n\n        <div className=\"mt-4 text-xs text-gray-500\">\n          <p>Stream ID: {streamProjectId}</p>\n          {buildId && <p>Build ID: {buildId}</p>}\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n\n// Extend window interface for StreamPixel SDK (optional if you want TS autocomplete)\ndeclare global {\n  interface Window {\n    StreamPixelApplication: any\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;;AAuDO,SAAS,aAAa,KAYT;QAZS,EAC3B,SAAS,EACT,OAAO,EACP,YAAY,EAAE,EACd,QAAQ,UAAU,EAClB,eAAe,IAAI,EACnB,aAAa,IAAI,EACjB,kBAAkB,IAAI,EACtB,QAAQ,IAAI,EACZ,SAAS,GAAG,EACZ,aAAa,KAAK,EAClB,oBAAoB,KAAK,EACP,GAZS;;IAa3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyD;IACxG,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB,MAAM,0BAA0B;;IAC5E,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB,MAAQ,4BAA4B;;IAE/E,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAO;IACjC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAW;IACzC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAW;IAE1C,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;2DAAmB;oBACvB,IAAI;wBACF,aAAa;wBACb,SAAS;wBACT,MAAM,WAAW,MAAM,MAAM,AAAC,iBAA0B,OAAV;wBAC9C,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;wBAClC,MAAM,eAAe,MAAM,SAAS,IAAI;wBACxC,MAAM,OAAO,aAAa,OAAO,IAAI;wBACrC,eAAe;wBACf,mBAAmB,KAAK,iBAAiB;wBACzC,MAAM,cAAc,cAAc,KAAK,MAAM,IAAI,CAAC;wBAClD,UAAU;wBACV,IAAI,CAAC,KAAK,iBAAiB,EAAE,MAAM,IAAI,MAAM;oBAC/C,EAAE,OAAO,KAAK;wBACZ,SAAS;oBACX,SAAU;wBACR,aAAa;oBACf;gBACF;;YACA,IAAI,WAAW;QACjB;iCAAG;QAAC;QAAW;KAAW;IAE1B,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;0CAAO;oBAAQ;gBAAU;;QAC3B;iCAAG,EAAE;IAEL,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAA,mBAAA,6BAAA,OAAQ,WAAW,KAAI,mBAAmB,CAAC,aAAa,CAAC,kBAAkB,OAAO,EAAE;gBACtF,QAAQ,GAAG,CAAC,gCAAgC;oBAC1C,WAAW,EAAE,mBAAA,6BAAA,OAAQ,WAAW;oBAChC;oBACA;oBACA,gBAAgB,kBAAkB,OAAO;gBAC3C;gBACA;YACF;QACF;iCAAG;QAAC,mBAAA,6BAAA,OAAQ,WAAW;QAAE;QAAiB;KAAU;QAIzC,wBACG,2BACE,6BACA,6BACP,sBACQ;IAPjB,mBAAmB;IACnB,MAAM,WAAW;QACf,SAAS,CAAA,yBAAA,mBAAA,6BAAA,OAAQ,cAAc,cAAtB,oCAAA,yBAA0B;QACnC,YAAY,CAAA,4BAAA,mBAAA,6BAAA,OAAQ,iBAAiB,cAAzB,uCAAA,4BAA6B;QACzC,cAAc,CAAA,8BAAA,mBAAA,6BAAA,OAAQ,mBAAmB,cAA3B,yCAAA,8BAA+B;QAC7C,cAAc,CAAA,8BAAA,mBAAA,6BAAA,OAAQ,mBAAmB,cAA3B,yCAAA,8BAA+B;QAC7C,OAAO,CAAA,uBAAA,mBAAA,6BAAA,OAAQ,YAAY,cAApB,kCAAA,uBAAwB;QAC/B,eAAe,CAAA,4BAAA,mBAAA,6BAAA,OAAQ,iBAAiB,cAAzB,uCAAA,4BAA6B;IAC9C;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,OAAO;YAI1B,oBACG,uBACC,MACE,0BACJ,OACC;QARjB,OAAO;YACL,aAAa;YACb,OAAO;YACP,YAAY,CAAA,qBAAA,mBAAA,6BAAA,OAAQ,UAAU,cAAlB,gCAAA,qBAAsB;YAClC,eAAe,CAAA,wBAAA,mBAAA,6BAAA,OAAQ,aAAa,cAArB,mCAAA,wBAAyB;YACxC,gBAAgB,CAAA,OAAC,mBAAA,6BAAA,OAAQ,cAAc,AAA+E,cAAtG,kBAAA,OAA2G;YAC3H,kBAAkB,CAAA,2BAAA,mBAAA,6BAAA,OAAQ,gBAAgB,cAAxB,sCAAA,2BAA4B;YAC9C,cAAc,CAAA,QAAC,mBAAA,6BAAA,OAAQ,YAAY,AAAkC,cAAvD,mBAAA,QAA4D;YAC1E,eAAe,CAAA,QAAC,mBAAA,6BAAA,OAAQ,aAAa,AAAkC,cAAxD,mBAAA,QAA6D;QAC9E;IACF;IAED,MAAM,mBAAmB;QACxB,IAAI,kBAAkB,OAAO,EAAE,QAAO,+BAA+B;QACrE,kBAAkB,OAAO,GAAG;QAC5B,aAAa;QACb,SAAS;QAIT,IAAI;YACF,MAAM,eAAe;YACrB,IAAI,CAAC,cAAc,MAAM,IAAI,MAAM;YACnC,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG,MAAM,CAAA,GAAA,+JAAA,CAAA,yBAAsB,AAAD,EAAE;YAC9E,UAAU,OAAO,GAAG;YACpB,UAAU,OAAO,GAAG;YACpB,aAAa,OAAO,GAAG;YAEvB,iBAAiB;YACjB,IAAI,YAAY,OAAO,IAAI,UAAU,WAAW,EAAE;gBAChD,IAAI,UAAU,WAAW,CAAC,UAAU,EAAE;oBACpC,UAAU,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,UAAU,WAAW;gBACpE;gBACA,YAAY,OAAO,CAAC,SAAS,GAAG;gBAChC,YAAY,OAAO,CAAC,WAAW,CAAC,UAAU,WAAW;YACvD;YAEA;YACA,iBAAiB,OAAO,GAAG;YAC3B,aAAa;QACf,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;YACxB,aAAa;QACf,SAAU;YACR,kBAAkB,OAAO,GAAG;QAC9B;IACF;IACE,MAAM,UAAU;QACf,IAAI;gBAIC,2BAAA,oBACA;YADJ,KAAI,qBAAA,UAAU,OAAO,cAAjB,0CAAA,4BAAA,mBAAmB,MAAM,cAAzB,gDAAA,0BAA2B,UAAU,EAAE,UAAU,OAAO,CAAC,MAAM,CAAC,UAAU;YAC9E,KAAI,qBAAA,UAAU,OAAO,cAAjB,yCAAA,mBAAmB,UAAU,EAAE,UAAU,OAAO,CAAC,UAAU;YAG/D,IAAI,YAAY,OAAO,EAAE,YAAY,OAAO,CAAC,SAAS,GAAG;QAC3D,EAAE,OAAO,KAAK,CAAC;QACf,eAAe;QACf,UAAU,OAAO,GAAG;QACpB,UAAU,OAAO,GAAG;QACpB,aAAa,OAAO,GAAG;QACvB,iBAAiB,OAAO,GAAG;QAC3B,kBAAkB,OAAO,GAAG;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,sBAAsB,SAAC;YAAc,wEAAY,CAAC;QACtD,IAAI,qBAAqB,aAAkB,eAAe,OAAO,MAAM,KAAK,QAAQ;YAClF,IAAI;gBACF,OAAO,MAAM,CAAC,WAAW,CAAC;oBACxB,MAAM,AAAC,aAAiB,OAAL;oBACnB;oBACA;oBACA,WAAW,IAAI,OAAO,WAAW;oBACjC,GAAG,IAAI;gBACT,GAAG;YACL,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,qCAAqC;YACpD;QACF;IACF;IAEA,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,qBAAqB,aAAkB,aAAa;YAEzD,MAAM;wDAAgB,CAAC;wBAChB,kBAAA;oBAAL,IAAI,GAAC,cAAA,MAAM,IAAI,cAAV,mCAAA,mBAAA,YAAY,IAAI,cAAhB,uCAAA,iBAAkB,UAAU,CAAC,gBAAe;oBAEjD,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI;oBACjC,QAAQ,GAAG,CAAC,oCAAoC,MAAM;oBAEtD,OAAQ;wBACN,KAAK;4BACH,IAAI,CAAC,WAAW;gCACd;4BACF;4BACA;wBACF,KAAK;4BACH,IAAI,WAAW;gCACb;4BACF;4BACA;wBACF,KAAK;4BACH,WAAW;4BACX;wBACF,KAAK;4BACH,WAAW;4BACX;wBACF,KAAK;4BACH,gBAAgB;4BAChB;wBACF,KAAK;4BACH,gBAAgB;4BAChB;wBACF,KAAK;4BACH,+BAA+B;4BAC/B,IAAI,UAAU,OAAO,IAAI,KAAK,KAAK,EAAE;gCACnC,+BAA+B;gCAC/B,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,YAAY;gCAClC,qCAAqC;gCACvC,OAAO,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,SAAS;gCACtC,kCAAkC;gCACpC;4BACF;4BACA;oBACJ;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;0CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;iCAAG;QAAC;QAAmB;QAAW;QAAW;KAAQ;IAErD,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,mBAAmB;gBACrB,oBAAoB,iBAAiB;oBACnC;oBACA;oBACA;oBACA;oBACA;oBACA;gBACF;YACF;QACF;iCAAG;QAAC;QAAmB;QAAW;QAAW;QAAc;QAAO;QAAS;KAAa;IAExF,uEAAuE;IACvE,MAAM,sBAAsB;QAC1B,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,UAAU,OAAO,CAAC,eAAe,GAAG;YAClC,QAAQ,GAAG,CAAC;YACZ,gBAAgB;YAChB,aAAa;YACb,gBAAgB;gBACE;YAAlB,kBAAkB,CAAA,4BAAA,mBAAA,6BAAA,OAAQ,iBAAiB,cAAzB,uCAAA,4BAA6B;YAC/C,oBAAoB;QACtB;QAEA,UAAU,OAAO,CAAC,kBAAkB,GAAG;YACrC,QAAQ,GAAG,CAAC;YACZ,gBAAgB;gBACE;YAAlB,kBAAkB,CAAA,4BAAA,mBAAA,6BAAA,OAAQ,iBAAiB,cAAzB,uCAAA,4BAA6B;YAC/C,oBAAoB;QACtB;QAEA,UAAU,OAAO,CAAC,iBAAiB,GAAG;YACpC,QAAQ,GAAG,CAAC;YACZ,gBAAgB;YAChB,kBAAkB;YAClB,sDAAsD;YACtD,oBAAoB;QACtB;QAEA,UAAU,OAAO,CAAC,kBAAkB,GAAG;YACrC,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC;YACZ,aAAa,QAAO,4CAA4C;YAChE,gBAAgB;YAChB,gBAAgB;YAChB,aAAa;YACb,oBAAoB;QACtB;QAEA,UAAU,OAAO,CAAC,YAAY,GAAG;YAC/B,QAAQ,GAAG,CAAC;YACZ,aAAa;YACb,aAAa;YACb,gBAAgB;YAChB,gBAAgB;gBACE;YAAlB,kBAAkB,CAAA,8BAAA,mBAAA,6BAAA,OAAQ,mBAAmB,cAA3B,yCAAA,8BAA+B;YACjD,oBAAoB;YAEpB,mDAAmD;YACnD,sBAAsB;gBACpB;YACF;QACF;QAEA,yDAAyD;QACzD,IAAI,UAAU,OAAO,CAAC,iBAAiB,EAAE;YACvC,UAAU,OAAO,CAAC,iBAAiB,GAAG;gBACpC,QAAQ,GAAG,CAAC;gBACZ,oBAAoB;YACtB;QACF;QAEA,IAAI,UAAU,OAAO,CAAC,oBAAoB,EAAE;YAC1C,UAAU,OAAO,CAAC,oBAAoB,GAAG,CAAC;gBACxC,QAAQ,GAAG,CAAC,wCAAwC;gBACpD,oBAAoB,wBAAwB;oBAAE;gBAAQ;YACxD;QACF;QAEA,gCAAgC;QAChC,IAAI,UAAU,OAAO,CAAC,eAAe,EAAE;YACrC,UAAU,OAAO,CAAC,eAAe,GAAG,CAAC;gBACnC,QAAQ,GAAG,CAAC,6BAA6B;gBACzC,oBAAoB,kBAAkB;oBAAE;gBAAQ;YAClD;QACF;IACF;IAEA,mBAAmB;IACnB,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC;QACZ,oBAAoB;QAEpB,QAAQ,GAAG,CAAC;QACZ,aAAa;QACb,gBAAgB;YACE;QAAlB,kBAAkB,CAAA,yBAAA,mBAAA,6BAAA,OAAQ,cAAc,cAAtB,oCAAA,yBAA0B;QAE5C;QACA,MAAM;QACN,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,cAAc;YAIlB,oBACA,2BAAA;QAJA,QAAQ,GAAG,CAAC;QACZ,oBAAoB;SAEpB,qBAAA,UAAU,OAAO,cAAjB,yCAAA,mBAAmB,UAAU;SAC7B,qBAAA,UAAU,OAAO,cAAjB,0CAAA,4BAAA,mBAAmB,MAAM,cAAzB,gDAAA,0BAA2B,UAAU;QAErC,yDAAyD;QACzD,sBAAsB;YACpB,aAAa;YACb,gBAAgB;QAClB;QAEA;QACA,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC;QACZ,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,WAAW;YAChC,WAAW,CAAC;YACZ,oBAAoB,gBAAgB;gBAAE,SAAS,CAAC;YAAQ;QAC1D;IACF;IACA,MAAM,mBAAmB;QACvB,IAAI,CAAC,aAAa,OAAO,EAAE;QAC3B,IAAI,CAAC,cAAc;gBACjB,yCAAA;aAAA,0CAAA,CAAA,wBAAA,aAAa,OAAO,EAAC,iBAAiB,cAAtC,8DAAA,6CAAA;QACF,OAAO;gBACL,0BAAA;aAAA,2BAAA,CAAA,YAAA,UAAS,cAAc,cAAvB,+CAAA,8BAAA;QACF;QACA,gBAAgB,CAAC;IACnB;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB;QAC3B,IAAI,qBAAoB,mBAAA,6BAAA,OAAQ,QAAQ,GAAE;YACxC,mBAAmB;YACnB,wBAAwB;QAC1B,OAAO;YACL,SAAS;QACX;IACF;IAEA,uBAAuB;IACvB,MAAM,oBAAoB;QACxB,MAAM,SAAS,uCAAgC,OAAO,QAAQ,CAAC,MAAM,GAAG;QACxE,MAAM,SAAS,IAAI;QAEnB,oCAAoC;QACpC,IAAI,CAAC,cAAc,OAAO,GAAG,CAAC,gBAAgB;QAC9C,IAAI,CAAC,YAAY,OAAO,GAAG,CAAC,cAAc;QAC1C,IAAI,CAAC,iBAAiB,OAAO,GAAG,CAAC,mBAAmB;QACpD,IAAI,mBAAA,6BAAA,OAAQ,WAAW,EAAE,OAAO,GAAG,CAAC,eAAe;QAEnD,MAAM,cAAc,OAAO,QAAQ;QACnC,MAAM,WAAW,AAAC,GAAkB,OAAhB,QAAO,WAAqB,OAAZ,WAAiD,OAArC,cAAc,AAAC,IAAe,OAAZ,eAAgB;QAElF,OAAO,AAAC,gEAGD,OADF,UAAS,gBAEN,OADD,OAAM,iBACE,OAAP,QAAO;IAyCjB;IACA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,MAAM;QACR,EAAE,UAAM,CAAE;IACZ;IACA,MAAM,eAAe;QACnB,wCAAmC;YACjC,MAAM,MAAM,AAAC,GAAqC,OAAnC,OAAO,QAAQ,CAAC,MAAM,EAAC,cAAsB,OAAV;YAClD,OAAO,IAAI,CAAC,KAAK,UAAU;QAC7B;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IACA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,yBAAyB;IACzB,IAAI,CAAA,mBAAA,6BAAA,OAAQ,mBAAmB,KAAI,CAAC,mBAAmB,sBAAsB;QAC3E,qBACE,6LAAC,4HAAA,CAAA,OAAI;YAAC,WAAW;sBACf,cAAA,6LAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAG,WAAU;sCAAsB;;;;;;sCACpC,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAC7B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6HAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,WAAW,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;8CAEzC,6LAAC,8HAAA,CAAA,SAAM;oCAAC,SAAS;oCAAsB,WAAU;;sDAC/C,6LAAC,+MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;wBAItC,uBAAS,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;IAKzD;IAEA,4BAA4B;IAC5B,IAAI,cAAc,mBAAmB;QACnC,qBACE,6LAAC;YAAI,WAAW,AAAC,0BAAmC,OAAV;sBAExC,cAAA,6LAAC;gBACC,KAAK;gBACL,WAAU;;kCAGV,6LAAC;wBACC,KAAK;wBACL,WAAU;;;;;;oBAGX,CAAC,aAAa,iBAAiB,YAAY,mBAC1C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;oCAAE,WAAU;8CAA4B;;;;;;8CACzC,6LAAC;oCAAE,WAAU;;wCAAqB;wCAAS;;;;;;;;;;;;;;;;;;oBAMhD,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;8CAAG;;;;;;;;;;;;;;;;;oBAMT,8BACC;;0CAEE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;wCAAC,WAAW;kDAAmB;;;;;;oCACpC,yBACC,6LAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;;4CAA0D;4CACnF,QAAQ,KAAK,CAAC,GAAG;4CAAG;;;;;;;;;;;;;0CAMlC,6LAAC;gCAAI,WAAU;;oCACZ,CAAC,0BACA,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,MAAK;wCACL,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;6DAInC,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,SAAQ;wCACR,MAAK;wCACL,WAAU;;0DAEV,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAKvC,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAET,wBAAU,6LAAC,+MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAAe,6LAAC,+MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAGlE,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQpC;IAEA,qBAAqB;IACrB,qBACE,6LAAC,4HAAA,CAAA,OAAI;QAAC,WAAW;;YACd,4BACC,6LAAC,4HAAA,CAAA,aAAU;0BACT,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC,4HAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,4HAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAEnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6HAAA,CAAA,QAAK;oCAAC,WAAW;8CAAmB;;;;;;gCACpC,yBACC,6LAAC,6HAAA,CAAA,QAAK;oCAAC,SAAQ;;wCAAU;wCACf,QAAQ,KAAK,CAAC,GAAG;wCAAG;;;;;;;gCAG/B,iCACC,6LAAC,8HAAA,CAAA,SAAM;oCAAC,MAAM;oCAAiB,cAAc;;sDAC3C,6LAAC,8HAAA,CAAA,gBAAa;4CAAC,OAAO;sDACpB,cAAA,6LAAC,8HAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;;kEAC7B,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;sDAIrC,6LAAC,8HAAA,CAAA,gBAAa;;8DACZ,6LAAC,8HAAA,CAAA,eAAY;;sEACX,6LAAC,8HAAA,CAAA,cAAW;sEAAC;;;;;;sEACb,6LAAC,8HAAA,CAAA,oBAAiB;sEAAC;;;;;;;;;;;;8DAErB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAAsB;;;;;;8EACvC,6LAAC;oEACC,WAAU;oEACV,MAAM;oEACN,QAAQ;oEACR,OAAO;;;;;;;;;;;;sEAGX,6LAAC,8HAAA,CAAA,SAAM;4DAAC,SAAS;4DAAe,WAAU;sEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUnE,6LAAC,4HAAA,CAAA,cAAW;;oBACT,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAK3C,6LAAC;wBACC,KAAK;wBACL,WAAU;wBACV,OAAO;4BACL,OAAO;4BACP,aAAa,AAAC,GAAW,OAAT,OAAM,KAAU,OAAP;4BACzB,WAAW;wBACb;;4BAGC,CAAC,aAAa,iBAAiB,YAAY,mBAC1C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;4CAAE,WAAU;sDAA4B;;;;;;sDACzC,6LAAC;4CAAE,WAAU;;gDAAqB;gDAAS;;;;;;;;;;;;;;;;;;4BAIhD,CAAC,aAAa,CAAC,aAAa,CAAC,SAAS,iBAAiB,gCACtD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAe;;;;;;8DAC5B,6LAAC;oDAAE,WAAU;8DAA2B,SAAS,YAAY;;;;;;gDAC5D,EAAC,mBAAA,6BAAA,OAAQ,WAAW,mBACnB,6LAAC,8HAAA,CAAA,SAAM;oDAAC,SAAS;oDAAY,MAAK;;sEAChC,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf,SAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;;4BAOlC,iBAAiB,yBAChB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;4CAAE,WAAU;sDAAe;;;;;;sDAC5B,6LAAC;4CAAE,WAAU;sDAAsB,SAAS,SAAS,KAAK;;;;;;;;;;;;;;;;;0CAMhE,6LAAC;gCACC,KAAK;gCACL,WAAU;gCACV,OAAO;oCAAE,QAAQ;oCAAG,eAAe,YAAY,SAAS;gCAAO;;;;;;;;;;;;oBAKlE,8BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ,iBAAiB,kBAAkB,iBAAiB,wBACnD,6LAAC,8HAAA,CAAA,SAAM;wCAAC,SAAS;wCAAY,UAAU;;0DACrC,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;+CAG5B,iBAAiB,6BACnB,6LAAC,8HAAA,CAAA,SAAM;wCAAC,QAAQ;;0DACd,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAClB,SAAS,UAAU;;;;;;6DAGtB,6LAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;;0DACjC,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAIlC,6LAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;kDAChC,wBACC,6LAAC,+MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,6LAAC,+MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;kDAC3C,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;wCAAc,OAAM;kDAC/D,cAAA,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMhC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCAAE;oCAAY;;;;;;;4BACd,yBAAW,6LAAC;;oCAAE;oCAAW;;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;GAnvBgB;KAAA", "debugId": null}}]}