import { notFound, redirect } from 'next/navigation'
import { createClient } from '@/utils/supabase/server'
import AdminProjectDetailClient from './client'

export default async function AdminProjectDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) redirect('/login')

  // Fetch profile
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()
  if (!profile || profile.role !== 'platform_admin') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
          <p className="text-gray-600 mt-2">You need platform admin privileges to access this page.</p>
        </div>
      </div>
    )
  }

  // Fetch the admin project (with builds and owner email)
  const { data: project, error } = await supabase
    .from('projects')
    .select(`
      *,
      builds (
        id, filename, original_filename, s3_key, version, status, is_current, file_size,
        streampixel_build_id, streampixel_status, error_message,
        created_at, updated_at
      ),
      profiles (
        email, role
      )
    `)
    .eq('id', id)
    .single()
  if (error || !project) notFound()

  return <AdminProjectDetailClient initialProject={project} />
}
