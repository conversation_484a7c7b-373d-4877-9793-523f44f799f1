import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'

interface StreamPixelWebhookPayload {
  id: string // StreamPixel project ID (success) | ZIP filename (failure)
  status: 'success' | 'failure'
  updated_at: string
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔔 StreamPixel webhook received')
    
    // Parse the webhook payload
    const payload: StreamPixelWebhookPayload = await request.json()
    console.log('Webhook payload:', payload)

    // Validate required fields
    if (!payload.id || !payload.status || !payload.updated_at) {
      console.error('Invalid webhook payload - missing required fields')
      return NextResponse.json(
        { error: 'Invalid payload: missing required fields (id, status, updated_at)' },
        { status: 400 }
      )
    }

    // Create Supabase client (service role for webhook)
    const supabase = await createClient()

    let build = null
    let updateData: Record<string, unknown> = {
      updated_at: new Date().toISOString()
    }

    if (payload.status === 'success') {
      // Success: payload.id is the StreamPixel project ID
      console.log('✅ StreamPixel upload successful for project:', payload.id)
      
      // Find the build by StreamPixel project ID
      const { data: builds, error: findError } = await supabase
        .from('builds')
        .select('*')
        .eq('streampixel_build_id', payload.id)
        .order('created_at', { ascending: false })
        .limit(1)

      if (findError) {
        console.error('Error finding build by StreamPixel ID:', findError)
        return NextResponse.json(
          { error: 'Failed to find build' },
          { status: 500 }
        )
      }

      if (!builds || builds.length === 0) {
        console.error('No build found with StreamPixel ID:', payload.id)
        return NextResponse.json(
          { error: 'Build not found' },
          { status: 404 }
        )
      }

      build = builds[0]
      
      // Update build status to active (final success state)
      updateData = {
        ...updateData,
        status: 'active',
        streampixel_status: 'live',
        is_current: true, // NOW make it current since StreamPixel confirmed success
        error_message: null
      }

    } else {
      // Failure: payload.id is the ZIP filename (timestamp_build-id.zip)
      console.log('❌ StreamPixel upload failed for file:', payload.id)

      // Extract build ID from filename
      // New format: [build-id].zip (filename IS the build ID)
      // Legacy format: [timestamp]_[build-id].zip (for backward compatibility)
      const filenameWithoutExt = payload.id.replace('.zip', '')
      const buildId = filenameWithoutExt.includes('_') ? filenameWithoutExt.split('_')[1] : filenameWithoutExt
      
      // Find the build by ID
      const { data: builds, error: findError } = await supabase
        .from('builds')
        .select('*')
        .eq('id', buildId)
        .single()

      if (findError) {
        console.error('Error finding build by ID:', findError)
        return NextResponse.json(
          { error: 'Failed to find build' },
          { status: 500 }
        )
      }

      if (!builds) {
        console.error('No build found with ID:', buildId)
        return NextResponse.json(
          { error: 'Build not found' },
          { status: 404 }
        )
      }

      build = builds
      
      // Update build status to failed
      updateData = {
        ...updateData,
        status: 'failed',
        streampixel_status: 'failed',
        error_message: 'StreamPixel processing failed',
        is_current: false // Failed builds cannot be current
      }
    }

    // Update the build in the database
    const { data: updatedBuild, error: updateError } = await supabase
      .from('builds')
      .update(updateData)
      .eq('id', build.id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating build:', updateError)
      return NextResponse.json(
        { error: 'Failed to update build' },
        { status: 500 }
      )
    }

    console.log('✅ Build updated successfully:', updatedBuild)

    // If this was a successful activation and the build should be current,
    // ensure no other builds are marked as current for this project
    if (payload.status === 'success' && updatedBuild.is_current) {
      await supabase
        .from('builds')
        .update({ 
          is_current: false,
          status: 'archived',
          updated_at: new Date().toISOString()
        })
        .eq('project_id', build.project_id)
        .neq('id', build.id)
        .eq('is_current', true)
    }

    return NextResponse.json({
      message: 'Webhook processed successfully',
      build: updatedBuild,
      status: payload.status
    })

  } catch (error) {
    console.error('❌ Error processing StreamPixel webhook:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
