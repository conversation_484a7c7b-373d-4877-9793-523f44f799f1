'use client'

import { useEffect, useRef, useState, useCallback, useMemo } from 'react'
import Uppy, { UppyFile } from '@uppy/core'
import AwsS3 from '@uppy/aws-s3'

import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import {
  AlertCircle,
  CheckCircle,
  Upload,
  Pause,
  Play,
  X,
  File,
  Clock,
  Zap,
  FileText
} from 'lucide-react'
import { clearAllServiceWorkers } from '@/lib/clear-service-workers'
import { cn } from '@/lib/utils'
import { Body, Meta, UploadResult } from '@uppy/core'


// Import minimal Uppy styles
import '@uppy/core/dist/style.min.css'

interface UppyFileUploadProps {
  projectId: string
  onUploadComplete?: (result: UploadResult<Meta, Body>) => void
  onUploadError?: (error: string) => void
  className?: string
}

export function UppyFileUpload({
  projectId,
  onUploadComplete,
  onUploadError,
  className
}: UppyFileUploadProps) {
  const uppyRef = useRef<Uppy<Meta> | null>(null)
  const fileInputRef = useRef<HTMLDivElement>(null)
  const hiddenFileInputRef = useRef<HTMLInputElement>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadSpeed, setUploadSpeed] = useState(0)
  const [timeRemaining, setTimeRemaining] = useState(0)
  const [isPaused, setIsPaused] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [selectedFile, setSelectedFile] = useState<UppyFile<Meta, Record<string, unknown>> | null>(null)
  const [isDragOver, setIsDragOver] = useState(false)

  useEffect(() => {
    // Clean up any interfering service workers
    clearAllServiceWorkers().catch(console.warn)

    // Initialize Uppy
    const uppy = new Uppy<Meta>({
      id: 'omnipixel-uploader',
      autoProceed: false,
      allowMultipleUploads: false,
      restrictions: {
        maxFileSize: 24 * 1024 * 1024 * 1024, // 24GB
        maxNumberOfFiles: 1,
        allowedFileTypes: ['.zip', 'application/zip', 'application/x-zip-compressed'],
      },
      meta: {
        projectId,
      },
    })

    // Configure AWS S3 multipart upload
    uppy.use(AwsS3, {
      shouldUseMultipart: (file) => (file.size || 0) > 100 * 1024 * 1024, // Use multipart for files > 100MB
      limit: 4, // Concurrent uploads
      retryDelays: [0, 1000, 3000, 5000],

      // For single file uploads
      getUploadParameters: async (file) => {
        try {
          const response = await fetch('/api/upload/uppy/single-params', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              filename: file.name,
              type: file.type,
              projectId,
            }),
          })

          if (!response.ok) {
            throw new Error('Failed to get upload parameters')
          }

          return await response.json()
        } catch (error) {
          console.error('Error getting upload parameters:', error)
          throw error
        }
      },

      // For multipart uploads - create the upload
      createMultipartUpload: async (file) => {
        try {
          const response = await fetch('/api/upload/uppy/multipart-params', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              filename: file.name,
              type: file.type,
              projectId,
              fileSize: file.size,
            }),
          })

          if (!response.ok) {
            throw new Error('Failed to create multipart upload')
          }

          return await response.json()
        } catch (error) {
          console.error('Error creating multipart upload:', error)
          throw error
        }
      },

      // Sign individual parts for multipart upload
      signPart: async (file, { uploadId, key, partNumber }) => {
        try {
          const response = await fetch('/api/upload/uppy/sign-part', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              key,
              uploadId,
              partNumber,
            }),
          })

          if (!response.ok) {
            throw new Error('Failed to sign part')
          }

          return await response.json()
        } catch (error) {
          console.error('Error signing part:', error)
          throw error
        }
      },

      // Complete multipart upload
      completeMultipartUpload: async (file, { uploadId, key, parts }) => {
        try {
          const response = await fetch('/api/upload/uppy/complete-multipart', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              key,
              uploadId,
              parts,
            }),
          })

          if (!response.ok) {
            throw new Error('Failed to complete multipart upload')
          }

          return await response.json()
        } catch (error) {
          console.error('Error completing multipart upload:', error)
          throw error
        }
      },

      // List parts (for resuming uploads)
      listParts: async (file, { uploadId, key }) => {
        try {
          const response = await fetch('/api/upload/uppy/list-parts', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              key,
              uploadId,
            }),
          })

          if (!response.ok) {
            throw new Error('Failed to list parts')
          }

          const data = await response.json()
          return data.parts || []
        } catch (error) {
          console.error('Error listing parts:', error)
          return []
        }
      },

      // Abort multipart upload
      abortMultipartUpload: async (file, { uploadId, key }) => {
        try {
          await fetch('/api/upload/uppy/abort-multipart', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              key,
              uploadId,
            }),
          })
        } catch (error) {
          console.error('Error aborting multipart upload:', error)
          // Don't throw - cancellation should not fail
        }
      },
    })

    // Using custom file input instead of Uppy FileInput plugin

    // Event listeners
    uppy.on('file-added', (file) => {
      console.log('File added to Uppy:', file.name, file)
      setSelectedFile(file)
      setError(null)
      setSuccess(null)
    })

    uppy.on('file-removed', (file) => {
      console.log('File removed from Uppy:', file?.name)
      setSelectedFile(null)
      setError(null)
      setSuccess(null)
    })

    uppy.on('restriction-failed', (file, error) => {
      console.error('File restriction failed:', file?.name, error)
      setError(`File restriction failed: ${error.message}`)
    })

    uppy.on('error', (error) => {
      console.error('Uppy error:', error)
      setError(`Upload error: ${error.message}`)
    })

    uppy.on('upload-error', (file, error, response) => {
      console.error('Upload error for file:', file?.name, error, response)
      setError(`Upload failed: ${error.message}`)
    })

    uppy.on('upload-start', () => {
      console.log('Upload started')
      setIsUploading(true)
      setError(null)
      setSuccess(null)
    })

    uppy.on('upload-progress', (file, progress) => {
      if (file && progress) {
        const percentage = Math.round((progress.bytesUploaded / (progress.bytesTotal || 1)) * 100)
        setUploadProgress(percentage)

        // Calculate upload speed and time remaining
        const elapsed = Date.now() - progress.uploadStarted
        const speed = progress.bytesUploaded / (elapsed / 1000) // bytes per second
        const remaining = ((progress.bytesTotal || 0) - progress.bytesUploaded) / speed // seconds

        setUploadSpeed(speed)
        setTimeRemaining(remaining)
      }
    })

    uppy.on('upload-success', async (file, response) => {
      console.log('Upload success:', file?.name)
      console.log('Full response object:', JSON.stringify(response, null, 2))

      try {
        // Extract S3 key from response and ensure bucket name is stripped
        let s3Key = ''

        console.log('Processing upload response:', response)

        // First, try to get the s3Key from the file's upload parameters (set during upload preparation)
        if (file?.meta?.key) {
          // Use the key that was set during upload preparation (contains full path)
          s3Key = file.meta.key
          console.log('Using s3_key from file meta:', s3Key)
        } else if (response.body?.build?.s3_key) {
          // Use the s3_key from our API response (this is from our complete endpoint)
          s3Key = response.body.build.s3_key
          console.log('Using s3_key from API response:', s3Key)
        } else if (response.uploadURL && typeof response.uploadURL === 'string') {
          try {
            // For single uploads, extract key from URL
            const url = new URL(response.uploadURL)
            let path = url.pathname.substring(1) // Remove leading slash
            // Remove bucket name if it's included in the path
            if (path.startsWith('omnipixel/')) {
              path = path.substring('omnipixel/'.length)
            }
            s3Key = path
          } catch (urlError) {
            console.warn('Invalid uploadURL format:', response.uploadURL, urlError)
            // Fallback: try to extract key from the uploadURL string directly
            if (response.uploadURL.includes('/')) {
              const parts = response.uploadURL.split('/')
              s3Key = parts[parts.length - 1] // Get the last part as filename
            }
          }
        } else if (response.body?.key) {
          // For multipart uploads, use the key directly but strip bucket name if present
          let path = String(response.body.key)
          if (path.startsWith('omnipixel/')) {
            path = path.substring('omnipixel/'.length)
          }
          s3Key = path
        } else if (response.body?.location) {
          // Extract from location URL and remove bucket name if present
          const url = new URL(response.body.location)
          let path = url.pathname.substring(1) // Remove leading slash
          // Remove bucket name if it's included in the path
          if (path.startsWith('omnipixel/')) {
            path = path.substring('omnipixel/'.length)
          }
          s3Key = path
        }

        // Final fallback: reconstruct the s3Key using the same logic as upload preparation
        if (!s3Key && file?.name) {
          console.warn('No s3_key found in response, attempting to reconstruct from filename:', file.name)

          // Try to get the full S3 key by calling our API to get user info and reconstruct
          // For now, use filename as fallback - the complete endpoint will handle this
          s3Key = file.name
          console.log('Using filename as s3Key fallback, complete endpoint will resolve full path')
        }

        console.log('Final s3Key for completion:', s3Key)

        // Complete the upload on the server
        const completeResponse = await fetch('/api/upload/uppy/complete', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            filename: file?.name,
            projectId,
            s3Key,
            fileSize: file?.size,
          }),
        })

        if (!completeResponse.ok) {
          throw new Error('Failed to complete upload')
        }

        const result = await completeResponse.json()
        console.log('Upload completion result:', result)
        setSuccess(`Upload completed successfully: ${file?.name}`)
        onUploadComplete?.(result)
      } catch (error) {
        console.error('Error completing upload:', error)
        setError('Upload completed but failed to register. Please try again.')
        onUploadError?.('Upload completed but failed to register. Please try again.')
      }
    })

    uppy.on('upload-error', (file, error) => {
      console.error('Upload error:', file?.name, error)
      setError(`Upload failed: ${error.message}`)
      onUploadError?.(error.message)
    })

    uppy.on('complete', (result) => {
      console.log('Upload complete:', result)
      setIsUploading(false)
      setUploadProgress(0)
      setUploadSpeed(0)
      setTimeRemaining(0)
    })

    uppy.on('cancel-all', () => {
      // Only log if we actually have files or are uploading
      if (uppy.getFiles().length > 0 || isUploading) {
        console.log('Upload cancelled - this may be due to user action or an error')
      }
      setIsUploading(false)
      setUploadProgress(0)
      setUploadSpeed(0)
      setTimeRemaining(0)
    })

    uppyRef.current = uppy

    return () => {
      uppy.destroy()
    }
  }, [projectId, onUploadComplete, onUploadError])

  // Control handlers
  const handlePauseResume = () => {
    if (!uppyRef.current) return

    if (isPaused) {
      uppyRef.current.resumeAll()
      setIsPaused(false)
    } else {
      uppyRef.current.pauseAll()
      setIsPaused(true)
    }
  }

  const handleCancel = () => {
    if (!uppyRef.current) return
    uppyRef.current.cancelAll()
    setSelectedFile(null)
  }

  const handleRemoveFile = () => {
    if (!uppyRef.current || !selectedFile) return
    uppyRef.current.removeFile(selectedFile.id)
    setSelectedFile(null)
  }

  const handleStartUpload = () => {
    if (!uppyRef.current) return
    uppyRef.current.upload()
  }

  // Drag and drop handlers
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0 && uppyRef.current) {
      try {
        const file = files[0]

        // Clear any existing files first
        uppyRef.current.getFiles().forEach(existingFile => {
          uppyRef.current?.removeFile(existingFile.id)
        })

        uppyRef.current.addFile({
          source: 'drag-drop',
          name: file.name,
          type: file.type,
          data: file,
          meta: {
            projectId,
          }
        })
      } catch (error) {
        console.error('Error adding file:', error)
        setError('Failed to add file. Please check the file type and size.')
      }
    }
  }, [projectId])

  const handleClick = useCallback(() => {
    // Prevent multiple clicks while uploading or if file already selected
    if (isUploading || selectedFile) {
      return
    }
    console.log('Upload area clicked, opening file dialog')
    hiddenFileInputRef.current?.click()
  }, [isUploading, selectedFile])

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    console.log('File input changed:', files)

    if (files && files.length > 0 && uppyRef.current) {
      try {
        const file = files[0]
        console.log('Adding file to Uppy:', file.name, file.type, file.size)

        // Clear any existing files first
        uppyRef.current.getFiles().forEach(existingFile => {
          uppyRef.current?.removeFile(existingFile.id)
        })

        uppyRef.current.addFile({
          source: 'file-input',
          name: file.name,
          type: file.type,
          data: file,
          meta: {
            projectId,
          }
        })
      } catch (error) {
        console.error('Error adding file to Uppy:', error)
        setError('Failed to add file. Please check the file type and size.')
      }
    }
    // Reset the input so the same file can be selected again
    e.target.value = ''
  }, [projectId])

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatTime = (seconds: number): string => {
    if (!isFinite(seconds) || seconds < 0) return '--'
    
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`
    } else {
      return `${secs}s`
    }
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Upload className="h-5 w-5" />
            <span>Upload Game Build</span>
          </CardTitle>
          <CardDescription>
            Upload your game build ZIP files with robust, pausable uploads supporting files up to 24GB
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* File Drop Zone */}
          <div
            className={cn(
              "relative border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer",
              isDragOver
                ? "border-primary bg-primary/5"
                : "border-muted-foreground/25 hover:border-muted-foreground/50",
              selectedFile && "border-green-500 bg-green-50"
            )}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={handleClick}
          >
            {/* Hidden file input */}
            <input
              ref={hiddenFileInputRef}
              type="file"
              accept=".zip,application/zip,application/x-zip-compressed"
              onChange={handleFileInputChange}
              className="hidden"
            />
            {!selectedFile ? (
              <div className="space-y-4">
                <div className="mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center">
                  <Upload className="h-6 w-6 text-muted-foreground" />
                </div>
                <div className="space-y-2">
                  <p className="text-lg font-medium">
                    {isDragOver ? "Drop your file here" : "Drag & drop your build file"}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    or click to browse files
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Supports ZIP files up to 24GB
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="mx-auto w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                  <File className="h-6 w-6 text-green-600" />
                </div>
                <div className="space-y-2">
                  <p className="text-lg font-medium text-green-700">File Selected</p>
                  <div className="flex items-center justify-center space-x-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">{selectedFile.name}</span>
                    <Badge variant="secondary">{formatBytes(selectedFile.size || 0)}</Badge>
                  </div>
                  <div className="flex items-center justify-center space-x-2 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleRemoveFile}
                      className="flex items-center space-x-1"
                    >
                      <X className="h-4 w-4" />
                      <span>Remove</span>
                    </Button>
                    {!isUploading && (
                      <Button
                        onClick={handleStartUpload}
                        className="flex items-center space-x-1"
                      >
                        <Upload className="h-4 w-4" />
                        <span>Start Upload</span>
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Upload Progress */}
          {isUploading && (
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base">Uploading {selectedFile?.name}</CardTitle>
                  <Badge variant={isPaused ? "secondary" : "default"}>
                    {isPaused ? "Paused" : "Uploading"}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Progress Bar */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">{uploadProgress}% complete</span>
                    <span className="text-muted-foreground">
                      {formatBytes(uploadSpeed)}/s
                    </span>
                  </div>
                  <Progress value={uploadProgress} className="h-2" />
                </div>

                {/* Upload Stats */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2 text-sm">
                    <Zap className="h-4 w-4 text-blue-500" />
                    <span className="text-muted-foreground">Speed:</span>
                    <span className="font-medium">{formatBytes(uploadSpeed)}/s</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <Clock className="h-4 w-4 text-orange-500" />
                    <span className="text-muted-foreground">Time left:</span>
                    <span className="font-medium">{formatTime(timeRemaining)}</span>
                  </div>
                </div>

                <Separator />

                {/* Upload Controls */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handlePauseResume}
                      className="flex items-center space-x-1"
                    >
                      {isPaused ? <Play className="h-4 w-4" /> : <Pause className="h-4 w-4" />}
                      <span>{isPaused ? 'Resume' : 'Pause'}</span>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCancel}
                      className="flex items-center space-x-1"
                    >
                      <X className="h-4 w-4" />
                      <span>Cancel</span>
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Upload continues in background when tab is minimized
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Success Message */}
          {success && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-700">
                {success}
              </AlertDescription>
            </Alert>
          )}

          {/* Error Message */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {error}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
